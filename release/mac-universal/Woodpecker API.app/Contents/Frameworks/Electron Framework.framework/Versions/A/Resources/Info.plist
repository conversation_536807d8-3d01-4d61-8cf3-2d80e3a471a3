<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CFBundleExecutable</key>
    <string>Electron Framework</string>
    <key>CFBundleIdentifier</key>
    <string>com.github.Electron.framework</string>
    <key>CFBundleName</key>
    <string>Electron Framework</string>
    <key>CFBundlePackageType</key>
    <string>FMWK</string>
    <key>CFBundleVersion</key>
    <string>38.1.0</string>
    <key>DTCompiler</key>
    <string>com.apple.compilers.llvm.clang.1_0</string>
    <key>DTSDKBuild</key>
    <string>23F73</string>
    <key>DTSDKName</key>
    <string>macosx14.5</string>
    <key>DTXcode</key>
    <string>1540</string>
    <key>DTXcodeBuild</key>
    <string>15F31d</string>
    <key>LSEnvironment</key>
    <dict>
      <key>MallocNanoZone</key>
      <string>0</string>
    </dict>
    <key>NSSupportsAutomaticGraphicsSwitching</key>
    <true/>
    <key>ElectronAsarIntegrity</key>
    <dict>
      <key>Resources/app.asar</key>
      <dict>
        <key>algorithm</key>
        <string>SHA256</string>
        <key>hash</key>
        <string>057cb0653023a8136998f04cce8ece2afa3103a01723c7c8c07e64bf75e7316b</string>
      </dict>
    </dict>
  </dict>
</plist>