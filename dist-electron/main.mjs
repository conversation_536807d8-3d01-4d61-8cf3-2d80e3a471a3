import { ipc<PERSON>ain, app, dialog, BrowserWindow, shell, Menu } from "electron";
import { fileURLToPath } from "node:url";
import path$2 from "node:path";
import Database from "better-sqlite3";
import path$1 from "path";
import fs from "fs";
import { l as logger } from "./logger-CDSbv2DK.js";
const CREATE_TABLES_SQL = {
  imports: `
    CREATE TABLE IF NOT EXISTS imports (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      filename TEXT NOT NULL,
      import_date DATETIME DEFAULT CURRENT_TIMESTAMP,
      status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
      lead_count INTEGER DEFAULT 0,
      error_messages TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `,
  leads: `
    CREATE TABLE IF NOT EXISTS leads (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      import_id INTEGER NOT NULL,
      company TEXT,
      contact_name TEXT,
      email TEXT,
      title TEXT,
      additional_fields TEXT, -- JSO<PERSON> string
      status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processed', 'exported', 'failed')),
      woodpecker_campaign_id TEXT,
      export_date DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (import_id) REFERENCES imports(id) ON DELETE CASCADE
    )
  `,
  generated_content: `
    CREATE TABLE IF NOT EXISTS generated_content (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      lead_id INTEGER NOT NULL,
      touchpoint_number INTEGER NOT NULL,
      content TEXT NOT NULL,
      content_type TEXT NOT NULL CHECK (content_type IN ('email', 'subject', 'template')),
      template_id TEXT,
      status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'approved', 'rejected')),
      generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      approved_at DATETIME,
      FOREIGN KEY (lead_id) REFERENCES leads(id) ON DELETE CASCADE
    )
  `,
  mappings: `
    CREATE TABLE IF NOT EXISTS mappings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      import_id INTEGER NOT NULL,
      csv_column TEXT NOT NULL,
      woodpecker_field TEXT NOT NULL,
      mapping_type TEXT DEFAULT 'direct' CHECK (mapping_type IN ('direct', 'computed', 'default')),
      is_active BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (import_id) REFERENCES imports(id) ON DELETE CASCADE
    )
  `,
  app_metadata: `
    CREATE TABLE IF NOT EXISTS app_metadata (
      key TEXT PRIMARY KEY,
      value TEXT NOT NULL,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `
};
const CREATE_INDEXES_SQL = [
  "CREATE INDEX IF NOT EXISTS idx_leads_import_id ON leads(import_id)",
  "CREATE INDEX IF NOT EXISTS idx_leads_email ON leads(email)",
  "CREATE INDEX IF NOT EXISTS idx_leads_status ON leads(status)",
  "CREATE INDEX IF NOT EXISTS idx_generated_content_lead_id ON generated_content(lead_id)",
  "CREATE INDEX IF NOT EXISTS idx_generated_content_status ON generated_content(status)",
  "CREATE INDEX IF NOT EXISTS idx_mappings_import_id ON mappings(import_id)",
  "CREATE INDEX IF NOT EXISTS idx_imports_status ON imports(status)",
  "CREATE INDEX IF NOT EXISTS idx_imports_date ON imports(import_date)"
];
const INITIAL_METADATA = [
  { key: "schema_version", value: "1.0.0" },
  { key: "created_at", value: (/* @__PURE__ */ new Date()).toISOString() },
  { key: "last_migration", value: "1.0.0" }
];
let appDataPath;
function setAppDataPath(path2) {
  appDataPath = path2;
}
function getAppDataPath() {
  return appDataPath;
}
function getDatabasePath() {
  const appDataPath2 = getAppDataPath();
  if (appDataPath2) {
    return path$1.join(appDataPath2, "leads.db");
  }
  return path$1.join(process.cwd(), "leads.db");
}
function initializeDatabase() {
  const dbPath = getDatabasePath();
  const dbDir = path$1.dirname(dbPath);
  if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
  }
  try {
    const db = new Database(dbPath);
    db.pragma("foreign_keys = ON");
    db.pragma("journal_mode = WAL");
    const createTables = db.transaction(() => {
      Object.values(CREATE_TABLES_SQL).forEach((sql) => {
        db.exec(sql);
      });
      CREATE_INDEXES_SQL.forEach((sql) => {
        db.exec(sql);
      });
      const checkMetadata = db.prepare("SELECT COUNT(*) as count FROM app_metadata");
      const metadataCount = checkMetadata.get();
      if (metadataCount.count === 0) {
        const insertMetadata = db.prepare("INSERT INTO app_metadata (key, value) VALUES (?, ?)");
        INITIAL_METADATA.forEach(({ key, value }) => {
          insertMetadata.run(key, value);
        });
      }
    });
    createTables();
    return db;
  } catch (error) {
    throw new Error(`Failed to initialize database: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}
function isDatabaseInitialized() {
  const dbPath = getDatabasePath();
  if (!fs.existsSync(dbPath)) {
    return false;
  }
  try {
    const db = new Database(dbPath, { readonly: true });
    const tables = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name IN ('imports', 'leads', 'generated_content', 'mappings', 'app_metadata')
    `).all();
    db.close();
    return tables.length === 5;
  } catch {
    return false;
  }
}
function getDatabase() {
  if (!isDatabaseInitialized()) {
    return initializeDatabase();
  }
  const dbPath = getDatabasePath();
  const db = new Database(dbPath);
  db.pragma("foreign_keys = ON");
  return db;
}
function closeDatabase(db) {
  try {
    if (db && typeof db.close === "function") {
      db.close();
    }
  } catch (error) {
    console.error("Error closing database:", error);
  }
}
class DatabasePool {
  connections = [];
  maxConnections = 5;
  getConnection() {
    if (this.connections.length > 0) {
      return this.connections.pop();
    }
    return getDatabase();
  }
  releaseConnection(db) {
    if (this.connections.length < this.maxConnections) {
      this.connections.push(db);
    } else {
      closeDatabase(db);
    }
  }
  closeAll() {
    this.connections.forEach((db) => closeDatabase(db));
    this.connections = [];
  }
}
const dbPool = new DatabasePool();
function withDatabase(operation) {
  const db = dbPool.getConnection();
  try {
    return operation(db);
  } finally {
    dbPool.releaseConnection(db);
  }
}
function withTransaction(operation) {
  return withDatabase((db) => {
    const transaction = db.transaction(() => operation(db));
    return transaction();
  });
}
function cleanup() {
  dbPool.closeAll();
}
if (typeof process !== "undefined") {
  process.on("exit", cleanup);
  process.on("SIGINT", cleanup);
  process.on("SIGTERM", cleanup);
}
class ImportsDAL {
  static create(importData) {
    return withDatabase((db) => {
      const stmt = db.prepare(`
        INSERT INTO imports (filename, import_date, status, lead_count, error_messages)
        VALUES (?, ?, ?, ?, ?)
      `);
      const result = stmt.run(
        importData.filename,
        importData.import_date || (/* @__PURE__ */ new Date()).toISOString(),
        importData.status,
        importData.lead_count || 0,
        importData.error_messages || null
      );
      return this.getById(result.lastInsertRowid);
    });
  }
  static getById(id) {
    return withDatabase((db) => {
      const stmt = db.prepare("SELECT * FROM imports WHERE id = ?");
      return stmt.get(id) || null;
    });
  }
  static getAll(filters, pagination) {
    return withDatabase((db) => {
      let query = "SELECT * FROM imports WHERE 1=1";
      const params = [];
      if (filters?.status) {
        query += " AND status = ?";
        params.push(filters.status);
      }
      if (filters?.filename) {
        query += " AND filename LIKE ?";
        params.push(`%${filters.filename}%`);
      }
      if (filters?.dateFrom) {
        query += " AND import_date >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND import_date <= ?";
        params.push(filters.dateTo);
      }
      query += " ORDER BY import_date DESC";
      if (pagination?.limit) {
        query += " LIMIT ?";
        params.push(pagination.limit);
        if (pagination?.offset) {
          query += " OFFSET ?";
          params.push(pagination.offset);
        }
      }
      const stmt = db.prepare(query);
      return stmt.all(...params);
    });
  }
  static update(id, updates) {
    return withDatabase((db) => {
      const fields = Object.keys(updates).filter((key) => updates[key] !== void 0);
      if (fields.length === 0) return this.getById(id);
      const setClause = fields.map((field) => `${field} = ?`).join(", ");
      const values = fields.map((field) => updates[field]);
      const stmt = db.prepare(`UPDATE imports SET ${setClause} WHERE id = ?`);
      const result = stmt.run(...values, id);
      return result.changes > 0 ? this.getById(id) : null;
    });
  }
  static updateStatus(id, status, errorMessages) {
    return this.update(id, { status, error_messages: errorMessages });
  }
  static updateLeadCount(id, leadCount) {
    return this.update(id, { lead_count: leadCount });
  }
  static delete(id) {
    return withTransaction((db) => {
      const stmt = db.prepare("DELETE FROM imports WHERE id = ?");
      const result = stmt.run(id);
      return result.changes > 0;
    });
  }
  static getCount(filters) {
    return withDatabase((db) => {
      let query = "SELECT COUNT(*) as count FROM imports WHERE 1=1";
      const params = [];
      if (filters?.status) {
        query += " AND status = ?";
        params.push(filters.status);
      }
      if (filters?.filename) {
        query += " AND filename LIKE ?";
        params.push(`%${filters.filename}%`);
      }
      if (filters?.dateFrom) {
        query += " AND import_date >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND import_date <= ?";
        params.push(filters.dateTo);
      }
      const stmt = db.prepare(query);
      const result = stmt.get(...params);
      return result.count;
    });
  }
  static getByStatus(status) {
    return this.getAll({ status });
  }
  static getRecent(limit = 10) {
    return this.getAll({}, { limit });
  }
}
class LeadsDAL {
  static create(leadData) {
    return withDatabase((db) => {
      const stmt = db.prepare(`
        INSERT INTO leads (
          import_id, company, contact_name, email, title, 
          additional_fields, status, woodpecker_campaign_id, export_date
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      const result = stmt.run(
        leadData.import_id,
        leadData.company || null,
        leadData.contact_name || null,
        leadData.email || null,
        leadData.title || null,
        leadData.additional_fields || null,
        leadData.status || "pending",
        leadData.woodpecker_campaign_id || null,
        leadData.export_date || null
      );
      return this.getById(result.lastInsertRowid);
    });
  }
  static bulkCreate(bulkData) {
    return withTransaction((db) => {
      const stmt = db.prepare(`
        INSERT INTO leads (
          import_id, company, contact_name, email, title, 
          additional_fields, status, woodpecker_campaign_id, export_date
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      const results = [];
      for (const lead of bulkData.leads) {
        const result = stmt.run(
          bulkData.import_id,
          lead.company || null,
          lead.contact_name || null,
          lead.email || null,
          lead.title || null,
          lead.additional_fields || null,
          lead.status || "pending",
          lead.woodpecker_campaign_id || null,
          lead.export_date || null
        );
        const created = this.getById(result.lastInsertRowid);
        if (created) results.push(created);
      }
      return results;
    });
  }
  static getById(id) {
    return withDatabase((db) => {
      const stmt = db.prepare("SELECT * FROM leads WHERE id = ?");
      return stmt.get(id) || null;
    });
  }
  static getAll(filters, pagination) {
    return withDatabase((db) => {
      let query = "SELECT * FROM leads WHERE 1=1";
      const params = [];
      if (filters?.import_id) {
        query += " AND import_id = ?";
        params.push(filters.import_id);
      }
      if (filters?.status) {
        query += " AND status = ?";
        params.push(filters.status);
      }
      if (filters?.company) {
        query += " AND company LIKE ?";
        params.push(`%${filters.company}%`);
      }
      if (filters?.email) {
        query += " AND email LIKE ?";
        params.push(`%${filters.email}%`);
      }
      if (filters?.contact_name) {
        query += " AND contact_name LIKE ?";
        params.push(`%${filters.contact_name}%`);
      }
      if (filters?.woodpecker_campaign_id) {
        query += " AND woodpecker_campaign_id = ?";
        params.push(filters.woodpecker_campaign_id);
      }
      if (filters?.dateFrom) {
        query += " AND created_at >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND created_at <= ?";
        params.push(filters.dateTo);
      }
      query += " ORDER BY created_at DESC";
      if (pagination?.limit) {
        query += " LIMIT ?";
        params.push(pagination.limit);
        if (pagination?.offset) {
          query += " OFFSET ?";
          params.push(pagination.offset);
        }
      }
      const stmt = db.prepare(query);
      return stmt.all(...params);
    });
  }
  static update(id, updates) {
    return withDatabase((db) => {
      const fields = Object.keys(updates).filter((key) => updates[key] !== void 0);
      if (fields.length === 0) return this.getById(id);
      const setClause = fields.map((field) => `${field} = ?`).join(", ");
      const values = fields.map((field) => updates[field]);
      const stmt = db.prepare(`UPDATE leads SET ${setClause} WHERE id = ?`);
      const result = stmt.run(...values, id);
      return result.changes > 0 ? this.getById(id) : null;
    });
  }
  static updateStatus(id, status) {
    return this.update(id, { status });
  }
  static updateWoodpeckerCampaign(id, campaignId, exportDate) {
    return this.update(id, {
      woodpecker_campaign_id: campaignId,
      export_date: exportDate || (/* @__PURE__ */ new Date()).toISOString(),
      status: "exported"
    });
  }
  static bulkUpdateStatus(ids, status) {
    return withTransaction((db) => {
      const placeholders = ids.map(() => "?").join(",");
      const stmt = db.prepare(`UPDATE leads SET status = ? WHERE id IN (${placeholders})`);
      const result = stmt.run(status, ...ids);
      return result.changes;
    });
  }
  static delete(id) {
    return withTransaction((db) => {
      const stmt = db.prepare("DELETE FROM leads WHERE id = ?");
      const result = stmt.run(id);
      return result.changes > 0;
    });
  }
  static deleteByImport(importId) {
    return withTransaction((db) => {
      const stmt = db.prepare("DELETE FROM leads WHERE import_id = ?");
      const result = stmt.run(importId);
      return result.changes;
    });
  }
  static getCount(filters) {
    return withDatabase((db) => {
      let query = "SELECT COUNT(*) as count FROM leads WHERE 1=1";
      const params = [];
      if (filters?.import_id) {
        query += " AND import_id = ?";
        params.push(filters.import_id);
      }
      if (filters?.status) {
        query += " AND status = ?";
        params.push(filters.status);
      }
      if (filters?.company) {
        query += " AND company LIKE ?";
        params.push(`%${filters.company}%`);
      }
      if (filters?.email) {
        query += " AND email LIKE ?";
        params.push(`%${filters.email}%`);
      }
      if (filters?.contact_name) {
        query += " AND contact_name LIKE ?";
        params.push(`%${filters.contact_name}%`);
      }
      if (filters?.woodpecker_campaign_id) {
        query += " AND woodpecker_campaign_id = ?";
        params.push(filters.woodpecker_campaign_id);
      }
      if (filters?.dateFrom) {
        query += " AND created_at >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND created_at <= ?";
        params.push(filters.dateTo);
      }
      const stmt = db.prepare(query);
      const result = stmt.get(...params);
      return result.count;
    });
  }
  static getByImport(importId, pagination) {
    return this.getAll({ import_id: importId }, pagination);
  }
  static getByStatus(status, pagination) {
    return this.getAll({ status }, pagination);
  }
  static searchByEmail(email) {
    return this.getAll({ email });
  }
  static searchByCompany(company, pagination) {
    return this.getAll({ company }, pagination);
  }
  static getExportedLeads(campaignId) {
    const filters = { status: "exported" };
    if (campaignId) {
      filters.woodpecker_campaign_id = campaignId;
    }
    return this.getAll(filters);
  }
  static getLeadsWithAdditionalFields() {
    return withDatabase((db) => {
      const stmt = db.prepare('SELECT * FROM leads WHERE additional_fields IS NOT NULL AND additional_fields != "" ORDER BY created_at DESC');
      return stmt.all();
    });
  }
}
class GeneratedContentDAL {
  static create(contentData) {
    return withDatabase((db) => {
      const stmt = db.prepare(`
        INSERT INTO generated_content (
          lead_id, touchpoint_number, content, content_type, 
          template_id, status, approved_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      const result = stmt.run(
        contentData.lead_id,
        contentData.touchpoint_number,
        contentData.content,
        contentData.content_type,
        contentData.template_id || null,
        contentData.status || "draft",
        contentData.approved_at || null
      );
      return this.getById(result.lastInsertRowid);
    });
  }
  static getById(id) {
    return withDatabase((db) => {
      const stmt = db.prepare("SELECT * FROM generated_content WHERE id = ?");
      return stmt.get(id) || null;
    });
  }
  static getAll(filters, pagination) {
    return withDatabase((db) => {
      let query = "SELECT * FROM generated_content WHERE 1=1";
      const params = [];
      if (filters?.lead_id) {
        query += " AND lead_id = ?";
        params.push(filters.lead_id);
      }
      if (filters?.touchpoint_number) {
        query += " AND touchpoint_number = ?";
        params.push(filters.touchpoint_number);
      }
      if (filters?.content_type) {
        query += " AND content_type = ?";
        params.push(filters.content_type);
      }
      if (filters?.template_id) {
        query += " AND template_id = ?";
        params.push(filters.template_id);
      }
      if (filters?.status) {
        query += " AND status = ?";
        params.push(filters.status);
      }
      if (filters?.dateFrom) {
        query += " AND generated_at >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND generated_at <= ?";
        params.push(filters.dateTo);
      }
      query += " ORDER BY generated_at DESC";
      if (pagination?.limit) {
        query += " LIMIT ?";
        params.push(pagination.limit);
        if (pagination?.offset) {
          query += " OFFSET ?";
          params.push(pagination.offset);
        }
      }
      const stmt = db.prepare(query);
      return stmt.all(...params);
    });
  }
  static update(id, updates) {
    return withDatabase((db) => {
      const fields = Object.keys(updates).filter((key) => updates[key] !== void 0);
      if (fields.length === 0) return this.getById(id);
      const setClause = fields.map((field) => `${field} = ?`).join(", ");
      const values = fields.map((field) => updates[field]);
      const stmt = db.prepare(`UPDATE generated_content SET ${setClause} WHERE id = ?`);
      const result = stmt.run(...values, id);
      return result.changes > 0 ? this.getById(id) : null;
    });
  }
  static updateStatus(id, status) {
    const updates = { status };
    if (status === "approved") {
      updates.approved_at = (/* @__PURE__ */ new Date()).toISOString();
    } else if (status === "draft" || status === "rejected") {
      updates.approved_at = null;
    }
    return this.update(id, updates);
  }
  static updateContent(id, content) {
    return this.update(id, { content, status: "draft", approved_at: null });
  }
  static delete(id) {
    return withTransaction((db) => {
      const stmt = db.prepare("DELETE FROM generated_content WHERE id = ?");
      const result = stmt.run(id);
      return result.changes > 0;
    });
  }
  static deleteByLead(leadId) {
    return withTransaction((db) => {
      const stmt = db.prepare("DELETE FROM generated_content WHERE lead_id = ?");
      const result = stmt.run(leadId);
      return result.changes;
    });
  }
  static getCount(filters) {
    return withDatabase((db) => {
      let query = "SELECT COUNT(*) as count FROM generated_content WHERE 1=1";
      const params = [];
      if (filters?.lead_id) {
        query += " AND lead_id = ?";
        params.push(filters.lead_id);
      }
      if (filters?.touchpoint_number) {
        query += " AND touchpoint_number = ?";
        params.push(filters.touchpoint_number);
      }
      if (filters?.content_type) {
        query += " AND content_type = ?";
        params.push(filters.content_type);
      }
      if (filters?.template_id) {
        query += " AND template_id = ?";
        params.push(filters.template_id);
      }
      if (filters?.status) {
        query += " AND status = ?";
        params.push(filters.status);
      }
      if (filters?.dateFrom) {
        query += " AND generated_at >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND generated_at <= ?";
        params.push(filters.dateTo);
      }
      const stmt = db.prepare(query);
      const result = stmt.get(...params);
      return result.count;
    });
  }
  static getByLead(leadId, pagination) {
    return this.getAll({ lead_id: leadId }, pagination);
  }
  static getByTouchpoint(leadId, touchpointNumber) {
    return this.getAll({ lead_id: leadId, touchpoint_number: touchpointNumber });
  }
  static getByStatus(status, pagination) {
    return this.getAll({ status }, pagination);
  }
  static getByContentType(contentType, pagination) {
    return this.getAll({ content_type: contentType }, pagination);
  }
  static getByTemplate(templateId, pagination) {
    return this.getAll({ template_id: templateId }, pagination);
  }
  static getApprovedContent(leadId) {
    const filters = { status: "approved" };
    if (leadId) {
      filters.lead_id = leadId;
    }
    return this.getAll(filters);
  }
  static getPendingApproval(pagination) {
    return this.getAll({ status: "draft" }, pagination);
  }
  static getLeadSequence(leadId) {
    return withDatabase((db) => {
      const stmt = db.prepare(`
        SELECT * FROM generated_content 
        WHERE lead_id = ? 
        ORDER BY touchpoint_number ASC, content_type ASC
      `);
      return stmt.all(leadId);
    });
  }
  static getContentStats() {
    return withDatabase((db) => {
      const totalStmt = db.prepare("SELECT COUNT(*) as count FROM generated_content");
      const total = totalStmt.get().count;
      const statusStmt = db.prepare("SELECT status, COUNT(*) as count FROM generated_content GROUP BY status");
      const statusResults = statusStmt.all();
      const byStatus = statusResults.reduce((acc, row) => {
        acc[row.status] = row.count;
        return acc;
      }, {});
      const typeStmt = db.prepare("SELECT content_type, COUNT(*) as count FROM generated_content GROUP BY content_type");
      const typeResults = typeStmt.all();
      const byType = typeResults.reduce((acc, row) => {
        acc[row.content_type] = row.count;
        return acc;
      }, {});
      return { total, byStatus, byType };
    });
  }
  static bulkUpdateStatus(ids, status) {
    return withTransaction((db) => {
      const placeholders = ids.map(() => "?").join(",");
      let query = `UPDATE generated_content SET status = ?`;
      const params = [status];
      if (status === "approved") {
        query += `, approved_at = ?`;
        params.push((/* @__PURE__ */ new Date()).toISOString());
      } else if (status === "draft" || status === "rejected") {
        query += `, approved_at = NULL`;
      }
      query += ` WHERE id IN (${placeholders})`;
      params.push(...ids);
      const stmt = db.prepare(query);
      const result = stmt.run(...params);
      return result.changes;
    });
  }
}
class MappingsDAL {
  static create(mappingData) {
    return withDatabase((db) => {
      const stmt = db.prepare(`
        INSERT INTO mappings (
          import_id, csv_column, woodpecker_field, mapping_type, is_active
        )
        VALUES (?, ?, ?, ?, ?)
      `);
      const result = stmt.run(
        mappingData.import_id,
        mappingData.csv_column,
        mappingData.woodpecker_field,
        mappingData.mapping_type || "direct",
        mappingData.is_active !== void 0 ? mappingData.is_active : true
      );
      return this.getById(result.lastInsertRowid);
    });
  }
  static bulkCreate(bulkData) {
    return withTransaction((db) => {
      const stmt = db.prepare(`
        INSERT INTO mappings (
          import_id, csv_column, woodpecker_field, mapping_type, is_active
        )
        VALUES (?, ?, ?, ?, ?)
      `);
      const results = [];
      for (const mapping of bulkData.mappings) {
        const result = stmt.run(
          bulkData.import_id,
          mapping.csv_column,
          mapping.woodpecker_field,
          mapping.mapping_type || "direct",
          mapping.is_active !== void 0 ? mapping.is_active : true
        );
        const created = this.getById(result.lastInsertRowid);
        if (created) results.push(created);
      }
      return results;
    });
  }
  static getById(id) {
    return withDatabase((db) => {
      const stmt = db.prepare("SELECT * FROM mappings WHERE id = ?");
      return stmt.get(id) || null;
    });
  }
  static getAll(filters, pagination) {
    return withDatabase((db) => {
      let query = "SELECT * FROM mappings WHERE 1=1";
      const params = [];
      if (filters?.import_id) {
        query += " AND import_id = ?";
        params.push(filters.import_id);
      }
      if (filters?.csv_column) {
        query += " AND csv_column LIKE ?";
        params.push(`%${filters.csv_column}%`);
      }
      if (filters?.woodpecker_field) {
        query += " AND woodpecker_field = ?";
        params.push(filters.woodpecker_field);
      }
      if (filters?.mapping_type) {
        query += " AND mapping_type = ?";
        params.push(filters.mapping_type);
      }
      if (filters?.is_active !== void 0) {
        query += " AND is_active = ?";
        params.push(filters.is_active);
      }
      if (filters?.dateFrom) {
        query += " AND created_at >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND created_at <= ?";
        params.push(filters.dateTo);
      }
      query += " ORDER BY created_at DESC";
      if (pagination?.limit) {
        query += " LIMIT ?";
        params.push(pagination.limit);
        if (pagination?.offset) {
          query += " OFFSET ?";
          params.push(pagination.offset);
        }
      }
      const stmt = db.prepare(query);
      return stmt.all(...params);
    });
  }
  static update(id, updates) {
    return withDatabase((db) => {
      const fields = Object.keys(updates).filter((key) => updates[key] !== void 0);
      if (fields.length === 0) return this.getById(id);
      const setClause = fields.map((field) => `${field} = ?`).join(", ");
      const values = fields.map((field) => updates[field]);
      const stmt = db.prepare(`UPDATE mappings SET ${setClause} WHERE id = ?`);
      const result = stmt.run(...values, id);
      return result.changes > 0 ? this.getById(id) : null;
    });
  }
  static updateActiveStatus(id, isActive) {
    return this.update(id, { is_active: isActive });
  }
  static updateMappingType(id, mappingType) {
    return this.update(id, { mapping_type: mappingType });
  }
  static bulkUpdateActiveStatus(ids, isActive) {
    return withTransaction((db) => {
      const placeholders = ids.map(() => "?").join(",");
      const stmt = db.prepare(`UPDATE mappings SET is_active = ? WHERE id IN (${placeholders})`);
      const result = stmt.run(isActive, ...ids);
      return result.changes;
    });
  }
  static delete(id) {
    return withTransaction((db) => {
      const stmt = db.prepare("DELETE FROM mappings WHERE id = ?");
      const result = stmt.run(id);
      return result.changes > 0;
    });
  }
  static deleteByImport(importId) {
    return withTransaction((db) => {
      const stmt = db.prepare("DELETE FROM mappings WHERE import_id = ?");
      const result = stmt.run(importId);
      return result.changes;
    });
  }
  static deactivateByImport(importId) {
    return withTransaction((db) => {
      const stmt = db.prepare("UPDATE mappings SET is_active = 0 WHERE import_id = ?");
      const result = stmt.run(importId);
      return result.changes;
    });
  }
  static getCount(filters) {
    return withDatabase((db) => {
      let query = "SELECT COUNT(*) as count FROM mappings WHERE 1=1";
      const params = [];
      if (filters?.import_id) {
        query += " AND import_id = ?";
        params.push(filters.import_id);
      }
      if (filters?.csv_column) {
        query += " AND csv_column LIKE ?";
        params.push(`%${filters.csv_column}%`);
      }
      if (filters?.woodpecker_field) {
        query += " AND woodpecker_field = ?";
        params.push(filters.woodpecker_field);
      }
      if (filters?.mapping_type) {
        query += " AND mapping_type = ?";
        params.push(filters.mapping_type);
      }
      if (filters?.is_active !== void 0) {
        query += " AND is_active = ?";
        params.push(filters.is_active);
      }
      if (filters?.dateFrom) {
        query += " AND created_at >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND created_at <= ?";
        params.push(filters.dateTo);
      }
      const stmt = db.prepare(query);
      const result = stmt.get(...params);
      return result.count;
    });
  }
  static getByImport(importId, activeOnly = true) {
    const filters = { import_id: importId };
    if (activeOnly) {
      filters.is_active = true;
    }
    return this.getAll(filters);
  }
  static getActiveMappings(importId) {
    const filters = { is_active: true };
    if (importId) {
      filters.import_id = importId;
    }
    return this.getAll(filters);
  }
  static getByMappingType(mappingType, pagination) {
    return this.getAll({ mapping_type: mappingType }, pagination);
  }
  static getByWoodpeckerField(woodpeckerField) {
    return this.getAll({ woodpecker_field: woodpeckerField });
  }
  static searchByCsvColumn(csvColumn) {
    return this.getAll({ csv_column: csvColumn });
  }
  static getMappingConfiguration(importId) {
    return withDatabase((db) => {
      const stmt = db.prepare(`
        SELECT csv_column, woodpecker_field 
        FROM mappings 
        WHERE import_id = ? AND is_active = 1
      `);
      const results = stmt.all(importId);
      return results.reduce((config, row) => {
        config[row.csv_column] = row.woodpecker_field;
        return config;
      }, {});
    });
  }
  static getWoodpeckerFieldUsage() {
    return withDatabase((db) => {
      const stmt = db.prepare(`
        SELECT woodpecker_field, COUNT(*) as usage_count 
        FROM mappings 
        WHERE is_active = 1 
        GROUP BY woodpecker_field 
        ORDER BY usage_count DESC
      `);
      const results = stmt.all();
      return results.reduce((usage, row) => {
        usage[row.woodpecker_field] = row.usage_count;
        return usage;
      }, {});
    });
  }
  static getMappingStats() {
    return withDatabase((db) => {
      const totalStmt = db.prepare("SELECT COUNT(*) as count FROM mappings");
      const total = totalStmt.get().count;
      const activeStmt = db.prepare("SELECT COUNT(*) as count FROM mappings WHERE is_active = 1");
      const active = activeStmt.get().count;
      const typeStmt = db.prepare("SELECT mapping_type, COUNT(*) as count FROM mappings GROUP BY mapping_type");
      const typeResults = typeStmt.all();
      const byType = typeResults.reduce((acc, row) => {
        acc[row.mapping_type] = row.count;
        return acc;
      }, {});
      return { total, active, byType };
    });
  }
  static duplicateImportMappings(sourceImportId, targetImportId) {
    return withTransaction((db) => {
      const sourceMappings = this.getByImport(sourceImportId, true);
      const bulkData = {
        import_id: targetImportId,
        mappings: sourceMappings.map((mapping) => ({
          csv_column: mapping.csv_column,
          woodpecker_field: mapping.woodpecker_field,
          mapping_type: mapping.mapping_type,
          is_active: mapping.is_active
        }))
      };
      return this.bulkCreate(bulkData);
    });
  }
  static validateMappingConfiguration(importId) {
    return withDatabase((db) => {
      const mappings = this.getByImport(importId, true);
      const errors = [];
      if (mappings.length === 0) {
        errors.push("No active mappings found for import");
      }
      const csvColumns = /* @__PURE__ */ new Set();
      const woodpeckerFields = /* @__PURE__ */ new Set();
      for (const mapping of mappings) {
        if (csvColumns.has(mapping.csv_column)) {
          errors.push(`Duplicate CSV column mapping: ${mapping.csv_column}`);
        }
        csvColumns.add(mapping.csv_column);
        if (woodpeckerFields.has(mapping.woodpecker_field)) {
          errors.push(`Duplicate Woodpecker field mapping: ${mapping.woodpecker_field}`);
        }
        woodpeckerFields.add(mapping.woodpecker_field);
      }
      return {
        valid: errors.length === 0,
        errors
      };
    });
  }
}
class AppMetadataDAL {
  static create(key, value) {
    return withDatabase((db) => {
      const stmt = db.prepare(`
        INSERT INTO app_metadata (key, value, updated_at)
        VALUES (?, ?, CURRENT_TIMESTAMP)
        ON CONFLICT(key) DO UPDATE SET 
          value = excluded.value,
          updated_at = CURRENT_TIMESTAMP
      `);
      stmt.run(key, value);
      return this.getByKey(key);
    });
  }
  static bulkCreate(metadata) {
    return withTransaction((db) => {
      const stmt = db.prepare(`
        INSERT INTO app_metadata (key, value, updated_at)
        VALUES (?, ?, CURRENT_TIMESTAMP)
        ON CONFLICT(key) DO UPDATE SET 
          value = excluded.value,
          updated_at = CURRENT_TIMESTAMP
      `);
      const results = [];
      for (const [key, value] of Object.entries(metadata)) {
        stmt.run(key, value);
        const created = this.getByKey(key);
        if (created) results.push(created);
      }
      return results;
    });
  }
  static getByKey(key) {
    return withDatabase((db) => {
      const stmt = db.prepare("SELECT * FROM app_metadata WHERE key = ?");
      return stmt.get(key) || null;
    });
  }
  static getValue(key) {
    const record = this.getByKey(key);
    return record?.value || null;
  }
  static getAll(filters, pagination) {
    return withDatabase((db) => {
      let query = "SELECT * FROM app_metadata WHERE 1=1";
      const params = [];
      if (filters?.keyPattern) {
        query += " AND key LIKE ?";
        params.push(`%${filters.keyPattern}%`);
      }
      if (filters?.valuePattern) {
        query += " AND value LIKE ?";
        params.push(`%${filters.valuePattern}%`);
      }
      if (filters?.dateFrom) {
        query += " AND updated_at >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND updated_at <= ?";
        params.push(filters.dateTo);
      }
      query += " ORDER BY updated_at DESC";
      if (pagination?.limit) {
        query += " LIMIT ?";
        params.push(pagination.limit);
        if (pagination?.offset) {
          query += " OFFSET ?";
          params.push(pagination.offset);
        }
      }
      const stmt = db.prepare(query);
      return stmt.all(...params);
    });
  }
  static update(key, value) {
    return this.create(key, value);
  }
  static delete(key) {
    return withDatabase((db) => {
      const stmt = db.prepare("DELETE FROM app_metadata WHERE key = ?");
      const result = stmt.run(key);
      return result.changes > 0;
    });
  }
  static bulkDelete(keys) {
    return withTransaction((db) => {
      const placeholders = keys.map(() => "?").join(",");
      const stmt = db.prepare(`DELETE FROM app_metadata WHERE key IN (${placeholders})`);
      const result = stmt.run(...keys);
      return result.changes;
    });
  }
  static getCount(filters) {
    return withDatabase((db) => {
      let query = "SELECT COUNT(*) as count FROM app_metadata WHERE 1=1";
      const params = [];
      if (filters?.keyPattern) {
        query += " AND key LIKE ?";
        params.push(`%${filters.keyPattern}%`);
      }
      if (filters?.valuePattern) {
        query += " AND value LIKE ?";
        params.push(`%${filters.valuePattern}%`);
      }
      if (filters?.dateFrom) {
        query += " AND updated_at >= ?";
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += " AND updated_at <= ?";
        params.push(filters.dateTo);
      }
      const stmt = db.prepare(query);
      const result = stmt.get(...params);
      return result.count;
    });
  }
  static getByKeyPattern(pattern) {
    return this.getAll({ keyPattern: pattern });
  }
  static getByValuePattern(pattern) {
    return this.getAll({ valuePattern: pattern });
  }
  static getAllAsObject() {
    return withDatabase((db) => {
      const stmt = db.prepare("SELECT key, value FROM app_metadata");
      const results = stmt.all();
      return results.reduce((obj, row) => {
        obj[row.key] = row.value;
        return obj;
      }, {});
    });
  }
  static getConfiguration() {
    return this.getByKeyPattern("config_").reduce((config, record) => {
      const configKey = record.key.replace("config_", "");
      config[configKey] = record.value;
      return config;
    }, {});
  }
  static setConfiguration(config) {
    const prefixedConfig = Object.entries(config).reduce((acc, [key, value]) => {
      acc[`config_${key}`] = value;
      return acc;
    }, {});
    return this.bulkCreate(prefixedConfig);
  }
  static getSchemaVersion() {
    return this.getValue("schema_version");
  }
  static setSchemaVersion(version) {
    return this.create("schema_version", version);
  }
  static getLastMigration() {
    return this.getValue("last_migration");
  }
  static setLastMigration(migration) {
    return this.create("last_migration", migration);
  }
  static getAppVersion() {
    return this.getValue("app_version");
  }
  static setAppVersion(version) {
    return this.create("app_version", version);
  }
  static getInstallationId() {
    return this.getValue("installation_id");
  }
  static setInstallationId(id) {
    return this.create("installation_id", id);
  }
  static getCreatedAt() {
    return this.getValue("created_at");
  }
  static setCreatedAt(timestamp) {
    return this.create("created_at", timestamp);
  }
  static getLastBackup() {
    return this.getValue("last_backup");
  }
  static setLastBackup(timestamp) {
    return this.create("last_backup", timestamp);
  }
  static getFeatureFlags() {
    const flags = this.getByKeyPattern("feature_");
    return flags.reduce((features, record) => {
      const featureName = record.key.replace("feature_", "");
      features[featureName] = record.value.toLowerCase() === "true";
      return features;
    }, {});
  }
  static setFeatureFlag(feature, enabled) {
    return this.create(`feature_${feature}`, enabled.toString());
  }
  static setFeatureFlags(flags) {
    const prefixedFlags = Object.entries(flags).reduce((acc, [key, value]) => {
      acc[`feature_${key}`] = value.toString();
      return acc;
    }, {});
    return this.bulkCreate(prefixedFlags);
  }
  static getUserPreferences() {
    const prefs = this.getByKeyPattern("pref_");
    return prefs.reduce((preferences, record) => {
      const prefName = record.key.replace("pref_", "");
      preferences[prefName] = record.value;
      return preferences;
    }, {});
  }
  static setUserPreference(preference, value) {
    return this.create(`pref_${preference}`, value);
  }
  static setUserPreferences(preferences) {
    const prefixedPrefs = Object.entries(preferences).reduce((acc, [key, value]) => {
      acc[`pref_${key}`] = value;
      return acc;
    }, {});
    return this.bulkCreate(prefixedPrefs);
  }
  static getStats() {
    const stats = this.getByKeyPattern("stat_");
    return stats.reduce((statistics, record) => {
      const statName = record.key.replace("stat_", "");
      statistics[statName] = record.value;
      return statistics;
    }, {});
  }
  static setStat(stat, value) {
    return this.create(`stat_${stat}`, value.toString());
  }
  static incrementStat(stat, increment = 1) {
    const currentValue = this.getValue(`stat_${stat}`);
    const newValue = (parseInt(currentValue || "0", 10) + increment).toString();
    return this.create(`stat_${stat}`, newValue);
  }
  static clearByPrefix(prefix) {
    return withTransaction((db) => {
      const stmt = db.prepare("DELETE FROM app_metadata WHERE key LIKE ?");
      const result = stmt.run(`${prefix}%`);
      return result.changes;
    });
  }
  static backup() {
    this.setLastBackup((/* @__PURE__ */ new Date()).toISOString());
    return this.getAll();
  }
  static restore(backup) {
    return withTransaction((db) => {
      db.prepare("DELETE FROM app_metadata").run();
      const stmt = db.prepare(`
        INSERT INTO app_metadata (key, value, updated_at)
        VALUES (?, ?, ?)
      `);
      let restored = 0;
      for (const record of backup) {
        stmt.run(record.key, record.value, record.updated_at || (/* @__PURE__ */ new Date()).toISOString());
        restored++;
      }
      return restored;
    });
  }
}
class AdvancedQueriesDAL {
  // Complex joins between tables
  static getImportsWithStats(pagination) {
    return withDatabase((db) => {
      let query = `
        SELECT 
          i.*,
          COALESCE(l.actual_lead_count, 0) as actual_lead_count,
          COALESCE(c.content_count, 0) as content_count,
          COALESCE(m.mapping_count, 0) as mapping_count
        FROM imports i
        LEFT JOIN (
          SELECT import_id, COUNT(*) as actual_lead_count
          FROM leads
          GROUP BY import_id
        ) l ON i.id = l.import_id
        LEFT JOIN (
          SELECT l.import_id, COUNT(gc.*) as content_count
          FROM leads l
          LEFT JOIN generated_content gc ON l.id = gc.lead_id
          GROUP BY l.import_id
        ) c ON i.id = c.import_id
        LEFT JOIN (
          SELECT import_id, COUNT(*) as mapping_count
          FROM mappings
          WHERE is_active = 1
          GROUP BY import_id
        ) m ON i.id = m.import_id
        ORDER BY i.import_date DESC
      `;
      if (pagination?.limit) {
        query += ` LIMIT ${pagination.limit}`;
        if (pagination?.offset) {
          query += ` OFFSET ${pagination.offset}`;
        }
      }
      const stmt = db.prepare(query);
      return stmt.all();
    });
  }
  static getLeadsWithContent(importId, pagination) {
    return withDatabase((db) => {
      let query = `
        SELECT 
          l.*,
          json_group_array(
            json_object(
              'id', gc.id,
              'touchpoint_number', gc.touchpoint_number,
              'content', gc.content,
              'content_type', gc.content_type,
              'template_id', gc.template_id,
              'status', gc.status,
              'generated_at', gc.generated_at,
              'approved_at', gc.approved_at
            )
          ) FILTER (WHERE gc.id IS NOT NULL) as content_items_json
        FROM leads l
        LEFT JOIN generated_content gc ON l.id = gc.lead_id
      `;
      const params = [];
      if (importId) {
        query += ` WHERE l.import_id = ?`;
        params.push(importId);
      }
      query += ` GROUP BY l.id ORDER BY l.created_at DESC`;
      if (pagination?.limit) {
        query += ` LIMIT ?`;
        params.push(pagination.limit);
        if (pagination?.offset) {
          query += ` OFFSET ?`;
          params.push(pagination.offset);
        }
      }
      const stmt = db.prepare(query);
      const results = stmt.all(...params);
      return results.map((row) => ({
        ...row,
        content_items: row.content_items_json ? JSON.parse(row.content_items_json) : []
      }));
    });
  }
  static getLeadsWithImportInfo(filters, pagination) {
    return withDatabase((db) => {
      let query = `
        SELECT 
          l.*,
          i.filename as import_filename,
          i.import_date,
          i.status as import_status
        FROM leads l
        INNER JOIN imports i ON l.import_id = i.id
        WHERE 1=1
      `;
      const params = [];
      if (filters?.importId) {
        query += ` AND l.import_id = ?`;
        params.push(filters.importId);
      }
      if (filters?.status) {
        query += ` AND l.status = ?`;
        params.push(filters.status);
      }
      if (filters?.dateFrom) {
        query += ` AND l.created_at >= ?`;
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += ` AND l.created_at <= ?`;
        params.push(filters.dateTo);
      }
      if (filters?.query) {
        query += ` AND (
          l.company LIKE ? OR 
          l.contact_name LIKE ? OR 
          l.email LIKE ? OR
          i.filename LIKE ?
        )`;
        const searchTerm = `%${filters.query}%`;
        params.push(searchTerm, searchTerm, searchTerm, searchTerm);
      }
      query += ` ORDER BY l.created_at DESC`;
      if (pagination?.limit) {
        query += ` LIMIT ?`;
        params.push(pagination.limit);
        if (pagination?.offset) {
          query += ` OFFSET ?`;
          params.push(pagination.offset);
        }
      }
      const stmt = db.prepare(query);
      return stmt.all(...params);
    });
  }
  static getContentWithLeadInfo(filters, pagination) {
    return withDatabase((db) => {
      let query = `
        SELECT 
          gc.*,
          l.company as lead_company,
          l.contact_name as lead_contact_name,
          l.email as lead_email,
          i.filename as import_filename
        FROM generated_content gc
        INNER JOIN leads l ON gc.lead_id = l.id
        INNER JOIN imports i ON l.import_id = i.id
        WHERE 1=1
      `;
      const params = [];
      if (filters?.status) {
        query += ` AND gc.status = ?`;
        params.push(filters.status);
      }
      if (filters?.dateFrom) {
        query += ` AND gc.generated_at >= ?`;
        params.push(filters.dateFrom);
      }
      if (filters?.dateTo) {
        query += ` AND gc.generated_at <= ?`;
        params.push(filters.dateTo);
      }
      if (filters?.query) {
        query += ` AND (
          gc.content LIKE ? OR 
          l.company LIKE ? OR 
          l.contact_name LIKE ? OR 
          l.email LIKE ?
        )`;
        const searchTerm = `%${filters.query}%`;
        params.push(searchTerm, searchTerm, searchTerm, searchTerm);
      }
      query += ` ORDER BY gc.generated_at DESC`;
      if (pagination?.limit) {
        query += ` LIMIT ?`;
        params.push(pagination.limit);
        if (pagination?.offset) {
          query += ` OFFSET ?`;
          params.push(pagination.offset);
        }
      }
      const stmt = db.prepare(query);
      return stmt.all(...params);
    });
  }
  // Aggregation queries for reporting
  static getReportingStats(dateFrom, dateTo) {
    return withDatabase((db) => {
      let dateFilter = "";
      const params = [];
      if (dateFrom && dateTo) {
        dateFilter = "WHERE created_at >= ? AND created_at <= ?";
        params.push(dateFrom, dateTo);
      } else if (dateFrom) {
        dateFilter = "WHERE created_at >= ?";
        params.push(dateFrom);
      } else if (dateTo) {
        dateFilter = "WHERE created_at <= ?";
        params.push(dateTo);
      }
      const totalImports = db.prepare(`SELECT COUNT(*) as count FROM imports ${dateFilter}`).get(...params);
      const totalLeads = db.prepare(`SELECT COUNT(*) as count FROM leads ${dateFilter}`).get(...params);
      const totalContent = db.prepare(`SELECT COUNT(*) as count FROM generated_content ${dateFilter.replace("created_at", "generated_at")}`).get(...params);
      const totalMappings = db.prepare(`SELECT COUNT(*) as count FROM mappings ${dateFilter}`).get(...params);
      const leadsByStatus = db.prepare(`SELECT status, COUNT(*) as count FROM leads ${dateFilter} GROUP BY status`).all(...params);
      const contentByStatus = db.prepare(`SELECT status, COUNT(*) as count FROM generated_content ${dateFilter.replace("created_at", "generated_at")} GROUP BY status`).all(...params);
      const importsByStatus = db.prepare(`SELECT status, COUNT(*) as count FROM imports ${dateFilter.replace("created_at", "import_date")} GROUP BY status`).all(...params);
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1e3).toISOString();
      const recentImports = db.prepare("SELECT COUNT(*) as count FROM imports WHERE import_date >= ?").get(sevenDaysAgo);
      const recentLeads = db.prepare("SELECT COUNT(*) as count FROM leads WHERE created_at >= ?").get(sevenDaysAgo);
      const recentContent = db.prepare("SELECT COUNT(*) as count FROM generated_content WHERE generated_at >= ?").get(sevenDaysAgo);
      return {
        totalImports: totalImports.count,
        totalLeads: totalLeads.count,
        totalContent: totalContent.count,
        totalMappings: totalMappings.count,
        leadsByStatus: leadsByStatus.reduce((acc, row) => {
          acc[row.status] = row.count;
          return acc;
        }, {}),
        contentByStatus: contentByStatus.reduce((acc, row) => {
          acc[row.status] = row.count;
          return acc;
        }, {}),
        importsByStatus: importsByStatus.reduce((acc, row) => {
          acc[row.status] = row.count;
          return acc;
        }, {}),
        recentActivity: {
          imports: recentImports.count,
          leads: recentLeads.count,
          content: recentContent.count
        }
      };
    });
  }
  // Search functionality across leads
  static searchLeads(query, pagination) {
    return this.getLeadsWithImportInfo({ query }, pagination);
  }
  static searchContent(query, pagination) {
    return this.getContentWithLeadInfo({ query }, pagination);
  }
  // Export query functions
  static getExportData(importId) {
    return withDatabase((db) => {
      const importStmt = db.prepare("SELECT * FROM imports WHERE id = ?");
      const importRecord = importStmt.get(importId);
      const leadsStmt = db.prepare("SELECT * FROM leads WHERE import_id = ? ORDER BY created_at");
      const leads = leadsStmt.all(importId);
      const mappingsStmt = db.prepare("SELECT * FROM mappings WHERE import_id = ? ORDER BY created_at");
      const mappings = mappingsStmt.all(importId);
      const leadIds = leads.map((lead) => lead.id).filter(Boolean);
      let content = [];
      if (leadIds.length > 0) {
        const placeholders = leadIds.map(() => "?").join(",");
        const contentStmt = db.prepare(`SELECT * FROM generated_content WHERE lead_id IN (${placeholders}) ORDER BY lead_id, touchpoint_number`);
        content = contentStmt.all(...leadIds);
      }
      return {
        import: importRecord,
        leads,
        mappings,
        content
      };
    });
  }
  static getLeadsForExport(filters) {
    return withDatabase((db) => {
      let query = `
        SELECT 
          l.*,
          json_group_array(
            json_object(
              'id', gc.id,
              'touchpoint_number', gc.touchpoint_number,
              'content', gc.content,
              'content_type', gc.content_type,
              'status', gc.status,
              'generated_at', gc.generated_at,
              'approved_at', gc.approved_at
            )
          ) FILTER (WHERE gc.id IS NOT NULL) as content_items_json
        FROM leads l
        LEFT JOIN generated_content gc ON l.id = gc.lead_id
        WHERE 1=1
      `;
      const params = [];
      if (filters?.importId) {
        query += ` AND l.import_id = ?`;
        params.push(filters.importId);
      }
      if (filters?.status) {
        query += ` AND l.status = ?`;
        params.push(filters.status);
      }
      if (filters?.hasContent) {
        query += ` AND EXISTS (SELECT 1 FROM generated_content WHERE lead_id = l.id)`;
      }
      query += ` GROUP BY l.id ORDER BY l.created_at`;
      const stmt = db.prepare(query);
      const results = stmt.all(...params);
      return results.map((row) => ({
        ...row,
        content_items: row.content_items_json ? JSON.parse(row.content_items_json) : []
      }));
    });
  }
  // Performance and analytics queries
  static getPerformanceMetrics() {
    return withDatabase((db) => {
      const avgLeadsStmt = db.prepare(`
        SELECT AVG(lead_count) as avg_leads
        FROM (
          SELECT COUNT(*) as lead_count
          FROM leads
          GROUP BY import_id
        )
      `);
      const avgLeads = avgLeadsStmt.get();
      const avgContentStmt = db.prepare(`
        SELECT AVG(content_count) as avg_content
        FROM (
          SELECT COUNT(*) as content_count
          FROM generated_content
          GROUP BY lead_id
        )
      `);
      const avgContent = avgContentStmt.get();
      const approvalRateStmt = db.prepare(`
        SELECT 
          (COUNT(CASE WHEN status = 'approved' THEN 1 END) * 100.0 / COUNT(*)) as approval_rate
        FROM generated_content
      `);
      const approvalRate = approvalRateStmt.get();
      const topMappingsStmt = db.prepare(`
        SELECT woodpecker_field, COUNT(*) as usage_count
        FROM mappings
        WHERE is_active = 1
        GROUP BY woodpecker_field
        ORDER BY usage_count DESC
        LIMIT 10
      `);
      const topMappings = topMappingsStmt.all();
      const topCompaniesStmt = db.prepare(`
        SELECT company, COUNT(*) as lead_count
        FROM leads
        WHERE company IS NOT NULL AND company != ''
        GROUP BY company
        ORDER BY lead_count DESC
        LIMIT 10
      `);
      const topCompanies = topCompaniesStmt.all();
      return {
        avgLeadsPerImport: Math.round((avgLeads.avg_leads || 0) * 100) / 100,
        avgContentPerLead: Math.round((avgContent.avg_content || 0) * 100) / 100,
        contentApprovalRate: Math.round((approvalRate.approval_rate || 0) * 100) / 100,
        mostUsedMappings: topMappings,
        topCompanies
      };
    });
  }
}
class DALError extends Error {
  constructor(message, operation, table, originalError) {
    super(message);
    this.operation = operation;
    this.table = table;
    this.originalError = originalError;
    this.name = "DALError";
  }
}
class ValidationError extends DALError {
  constructor(message, operation, table, field, value) {
    super(message, operation, table);
    this.field = field;
    this.value = value;
    this.name = "ValidationError";
  }
}
let NotFoundError$1 = class NotFoundError extends DALError {
  constructor(operation, table, id) {
    super(`Record not found: ${table} with id ${id}`, operation, table);
    this.id = id;
    this.name = "NotFoundError";
  }
};
class ForeignKeyError extends DALError {
  constructor(operation, table, foreignKey, foreignValue) {
    super(`Foreign key constraint failed: ${foreignKey} = ${foreignValue}`, operation, table);
    this.foreignKey = foreignKey;
    this.foreignValue = foreignValue;
    this.name = "ForeignKeyError";
  }
}
class UniqueConstraintError extends DALError {
  constructor(operation, table, field, value) {
    super(`Unique constraint violation: ${field} = ${value}`, operation, table);
    this.field = field;
    this.value = value;
    this.name = "UniqueConstraintError";
  }
}
class TransactionError extends DALError {
  constructor(message, operation, originalError) {
    super(message, operation, "transaction", originalError);
    this.name = "TransactionError";
  }
}
function handleIpcError(error, operation) {
  logger.error("IPC", `Error in ${operation}`, error instanceof Error ? error : new Error(String(error)));
  if (error instanceof ValidationError) {
    return {
      success: false,
      error: {
        type: "ValidationError",
        message: error.message,
        code: "VALIDATION_FAILED",
        details: error.details
      }
    };
  }
  if (error instanceof NotFoundError$1) {
    return {
      success: false,
      error: {
        type: "NotFoundError",
        message: error.message,
        code: "NOT_FOUND"
      }
    };
  }
  if (error instanceof ForeignKeyError) {
    return {
      success: false,
      error: {
        type: "ForeignKeyError",
        message: error.message,
        code: "FOREIGN_KEY_CONSTRAINT"
      }
    };
  }
  if (error instanceof UniqueConstraintError) {
    return {
      success: false,
      error: {
        type: "UniqueConstraintError",
        message: error.message,
        code: "UNIQUE_CONSTRAINT"
      }
    };
  }
  if (error instanceof TransactionError) {
    return {
      success: false,
      error: {
        type: "TransactionError",
        message: error.message,
        code: "TRANSACTION_FAILED"
      }
    };
  }
  if (error instanceof DALError) {
    return {
      success: false,
      error: {
        type: "DALError",
        message: error.message,
        code: "DATABASE_ERROR"
      }
    };
  }
  const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
  return {
    success: false,
    error: {
      type: "UnknownError",
      message: errorMessage,
      code: "UNKNOWN_ERROR"
    }
  };
}
function validateInput(data, requiredFields) {
  if (!data || typeof data !== "object") {
    throw new ValidationError("Input data is required and must be an object");
  }
  const missingFields = requiredFields.filter((field) => {
    const value = data[field];
    return value === void 0 || value === null || value === "";
  });
  if (missingFields.length > 0) {
    throw new ValidationError(
      `Missing required fields: ${missingFields.join(", ")}`,
      { missingFields }
    );
  }
}
function sanitizeInput(data) {
  if (data === null || data === void 0) {
    return data;
  }
  if (typeof data === "string") {
    return data.trim();
  }
  if (typeof data === "number") {
    if (isNaN(data) || !isFinite(data)) {
      throw new ValidationError("Invalid number value");
    }
    return data;
  }
  if (typeof data === "boolean") {
    return data;
  }
  if (Array.isArray(data)) {
    return data.map((item) => sanitizeInput(item));
  }
  if (typeof data === "object") {
    const sanitized = {};
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeInput(value);
    }
    return sanitized;
  }
  return data;
}
function createSuccessResponse(data) {
  return {
    success: true,
    data
  };
}
function logIpcOperation(operation, args) {
  logger.debug("IPC", `Operation: ${operation}`, args ? { args } : void 0);
}
function setupImportsHandlers() {
  ipcMain.handle("ipc:imports:create", async (_, data) => {
    try {
      validateInput(data, ["filename", "file_path", "status"]);
      return ImportsDAL.create(data);
    } catch (error) {
      return handleIpcError(error, "imports:create");
    }
  });
  ipcMain.handle("ipc:imports:getAll", async (_, options) => {
    try {
      return ImportsDAL.getAll(options);
    } catch (error) {
      return handleIpcError(error, "imports:getAll");
    }
  });
  ipcMain.handle("ipc:imports:getById", async (_, id) => {
    try {
      validateInput({ id }, ["id"]);
      return ImportsDAL.getById(id);
    } catch (error) {
      return handleIpcError(error, "imports:getById");
    }
  });
  ipcMain.handle("ipc:imports:update", async (_, id, data) => {
    try {
      validateInput({ id }, ["id"]);
      return ImportsDAL.update(id, data);
    } catch (error) {
      return handleIpcError(error, "imports:update");
    }
  });
  ipcMain.handle("ipc:imports:delete", async (_, id) => {
    try {
      validateInput({ id }, ["id"]);
      return ImportsDAL.delete(id);
    } catch (error) {
      return handleIpcError(error, "imports:delete");
    }
  });
  console.log("Imports IPC handlers setup complete");
}
function setupLeadsHandlers() {
  ipcMain.handle("ipc:leads:create", async (_, data) => {
    try {
      validateInput(data, ["import_id", "email"]);
      return LeadsDAL.create(data);
    } catch (error) {
      return handleIpcError(error, "leads:create");
    }
  });
  ipcMain.handle("ipc:leads:bulkCreate", async (_, leads) => {
    try {
      validateInput({ leads }, ["leads"]);
      if (!Array.isArray(leads) || leads.length === 0) {
        throw new Error("Leads array is required and must not be empty");
      }
      return LeadsDAL.bulkCreate(leads);
    } catch (error) {
      return handleIpcError(error, "leads:bulkCreate");
    }
  });
  ipcMain.handle("ipc:leads:getAll", async (_, options) => {
    try {
      return LeadsDAL.getAll(options);
    } catch (error) {
      return handleIpcError(error, "leads:getAll");
    }
  });
  ipcMain.handle("ipc:leads:getById", async (_, id) => {
    try {
      validateInput({ id }, ["id"]);
      return LeadsDAL.getById(id);
    } catch (error) {
      return handleIpcError(error, "leads:getById");
    }
  });
  ipcMain.handle("ipc:leads:getByImport", async (_, importId) => {
    try {
      validateInput({ importId }, ["importId"]);
      return LeadsDAL.getByImport(importId);
    } catch (error) {
      return handleIpcError(error, "leads:getByImport");
    }
  });
  ipcMain.handle("ipc:leads:update", async (_, id, data) => {
    try {
      validateInput({ id }, ["id"]);
      return LeadsDAL.update(id, data);
    } catch (error) {
      return handleIpcError(error, "leads:update");
    }
  });
  ipcMain.handle("ipc:leads:delete", async (_, id) => {
    try {
      validateInput({ id }, ["id"]);
      return LeadsDAL.delete(id);
    } catch (error) {
      return handleIpcError(error, "leads:delete");
    }
  });
  ipcMain.handle("ipc:leads:search", async (_, query, options) => {
    try {
      validateInput({ query }, ["query"]);
      return LeadsDAL.search(query, options);
    } catch (error) {
      return handleIpcError(error, "leads:search");
    }
  });
  console.log("Leads IPC handlers setup complete");
}
function setupGeneratedContentHandlers() {
  ipcMain.handle("ipc:content:create", async (_, data) => {
    try {
      validateInput(data, ["lead_id", "touchpoint_number", "content_type"]);
      return GeneratedContentDAL.create(data);
    } catch (error) {
      return handleIpcError(error, "content:create");
    }
  });
  ipcMain.handle("ipc:content:getByLead", async (_, leadId) => {
    try {
      validateInput({ leadId }, ["leadId"]);
      return GeneratedContentDAL.getByLead(leadId);
    } catch (error) {
      return handleIpcError(error, "content:getByLead");
    }
  });
  ipcMain.handle("ipc:content:getByTouchpoint", async (_, touchpoint, options) => {
    try {
      validateInput({ touchpoint }, ["touchpoint"]);
      return GeneratedContentDAL.getByTouchpoint(touchpoint, options);
    } catch (error) {
      return handleIpcError(error, "content:getByTouchpoint");
    }
  });
  ipcMain.handle("ipc:content:update", async (_, id, data) => {
    try {
      validateInput({ id }, ["id"]);
      return GeneratedContentDAL.update(id, data);
    } catch (error) {
      return handleIpcError(error, "content:update");
    }
  });
  ipcMain.handle("ipc:content:delete", async (_, id) => {
    try {
      validateInput({ id }, ["id"]);
      return GeneratedContentDAL.delete(id);
    } catch (error) {
      return handleIpcError(error, "content:delete");
    }
  });
  console.log("Generated Content IPC handlers setup complete");
}
function setupMappingsHandlers() {
  ipcMain.handle("ipc:mappings:create", async (_, data) => {
    try {
      validateInput(data, ["import_id", "source_field", "target_field"]);
      return MappingsDAL.create(data);
    } catch (error) {
      return handleIpcError(error, "mappings:create");
    }
  });
  ipcMain.handle("ipc:mappings:getByImport", async (_, importId) => {
    try {
      validateInput({ importId }, ["importId"]);
      return MappingsDAL.getByImport(importId);
    } catch (error) {
      return handleIpcError(error, "mappings:getByImport");
    }
  });
  ipcMain.handle("ipc:mappings:getActive", async (_, options) => {
    try {
      return MappingsDAL.getActive(options);
    } catch (error) {
      return handleIpcError(error, "mappings:getActive");
    }
  });
  ipcMain.handle("ipc:mappings:update", async (_, id, data) => {
    try {
      validateInput({ id }, ["id"]);
      return MappingsDAL.update(id, data);
    } catch (error) {
      return handleIpcError(error, "mappings:update");
    }
  });
  ipcMain.handle("ipc:mappings:delete", async (_, id) => {
    try {
      validateInput({ id }, ["id"]);
      return MappingsDAL.delete(id);
    } catch (error) {
      return handleIpcError(error, "mappings:delete");
    }
  });
  console.log("Mappings IPC handlers setup complete");
}
function setupAppMetadataHandlers() {
  ipcMain.handle("ipc:metadata:get", async (_, key) => {
    try {
      validateInput({ key }, ["key"]);
      return AppMetadataDAL.get(key);
    } catch (error) {
      return handleIpcError(error, "metadata:get");
    }
  });
  ipcMain.handle("ipc:metadata:set", async (_, key, value) => {
    try {
      validateInput({ key, value }, ["key", "value"]);
      return AppMetadataDAL.set(key, value);
    } catch (error) {
      return handleIpcError(error, "metadata:set");
    }
  });
  ipcMain.handle("ipc:metadata:delete", async (_, key) => {
    try {
      validateInput({ key }, ["key"]);
      return AppMetadataDAL.delete(key);
    } catch (error) {
      return handleIpcError(error, "metadata:delete");
    }
  });
  ipcMain.handle("ipc:metadata:getAll", async (_, options) => {
    try {
      return AppMetadataDAL.getAll(options);
    } catch (error) {
      return handleIpcError(error, "metadata:getAll");
    }
  });
  console.log("App Metadata IPC handlers setup complete");
}
function setupAdvancedQueriesHandlers() {
  ipcMain.handle("ipc:queries:getLeadsWithContent", async (_, options) => {
    try {
      return AdvancedQueriesDAL.getLeadsWithContent(options);
    } catch (error) {
      return handleIpcError(error, "queries:getLeadsWithContent");
    }
  });
  ipcMain.handle("ipc:queries:getImportSummary", async (_, importId) => {
    try {
      return AdvancedQueriesDAL.getImportSummary(importId);
    } catch (error) {
      return handleIpcError(error, "queries:getImportSummary");
    }
  });
  ipcMain.handle("ipc:queries:getContentStats", async (_) => {
    try {
      return AdvancedQueriesDAL.getContentStats();
    } catch (error) {
      return handleIpcError(error, "queries:getContentStats");
    }
  });
  ipcMain.handle("ipc:queries:exportData", async (_, format, options) => {
    try {
      validateInput({ format }, ["format"]);
      return AdvancedQueriesDAL.exportData(format, options);
    } catch (error) {
      return handleIpcError(error, "queries:exportData");
    }
  });
  console.log("Advanced Queries IPC handlers setup complete");
}
function __classPrivateFieldSet(receiver, state, value, kind, f) {
  if (typeof state === "function" ? receiver !== state || true : !state.has(receiver))
    throw new TypeError("Cannot write private member to an object whose class did not declare it");
  return state.set(receiver, value), value;
}
function __classPrivateFieldGet(receiver, state, kind, f) {
  if (kind === "a" && !f)
    throw new TypeError("Private accessor was defined without a getter");
  if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver))
    throw new TypeError("Cannot read private member from an object whose class did not declare it");
  return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
}
let uuid4 = function() {
  const { crypto: crypto2 } = globalThis;
  if (crypto2?.randomUUID) {
    uuid4 = crypto2.randomUUID.bind(crypto2);
    return crypto2.randomUUID();
  }
  const u8 = new Uint8Array(1);
  const randomByte = crypto2 ? () => crypto2.getRandomValues(u8)[0] : () => Math.random() * 255 & 255;
  return "10000000-1000-4000-8000-100000000000".replace(/[018]/g, (c) => (+c ^ randomByte() & 15 >> +c / 4).toString(16));
};
function isAbortError(err) {
  return typeof err === "object" && err !== null && // Spec-compliant fetch implementations
  ("name" in err && err.name === "AbortError" || // Expo fetch
  "message" in err && String(err.message).includes("FetchRequestCanceledException"));
}
const castToError = (err) => {
  if (err instanceof Error)
    return err;
  if (typeof err === "object" && err !== null) {
    try {
      if (Object.prototype.toString.call(err) === "[object Error]") {
        const error = new Error(err.message, err.cause ? { cause: err.cause } : {});
        if (err.stack)
          error.stack = err.stack;
        if (err.cause && !error.cause)
          error.cause = err.cause;
        if (err.name)
          error.name = err.name;
        return error;
      }
    } catch {
    }
    try {
      return new Error(JSON.stringify(err));
    } catch {
    }
  }
  return new Error(err);
};
class AnthropicError extends Error {
}
class APIError extends AnthropicError {
  constructor(status, error, message, headers) {
    super(`${APIError.makeMessage(status, error, message)}`);
    this.status = status;
    this.headers = headers;
    this.requestID = headers?.get("request-id");
    this.error = error;
  }
  static makeMessage(status, error, message) {
    const msg = error?.message ? typeof error.message === "string" ? error.message : JSON.stringify(error.message) : error ? JSON.stringify(error) : message;
    if (status && msg) {
      return `${status} ${msg}`;
    }
    if (status) {
      return `${status} status code (no body)`;
    }
    if (msg) {
      return msg;
    }
    return "(no status code or body)";
  }
  static generate(status, errorResponse, message, headers) {
    if (!status || !headers) {
      return new APIConnectionError({ message, cause: castToError(errorResponse) });
    }
    const error = errorResponse;
    if (status === 400) {
      return new BadRequestError(status, error, message, headers);
    }
    if (status === 401) {
      return new AuthenticationError(status, error, message, headers);
    }
    if (status === 403) {
      return new PermissionDeniedError(status, error, message, headers);
    }
    if (status === 404) {
      return new NotFoundError2(status, error, message, headers);
    }
    if (status === 409) {
      return new ConflictError(status, error, message, headers);
    }
    if (status === 422) {
      return new UnprocessableEntityError(status, error, message, headers);
    }
    if (status === 429) {
      return new RateLimitError(status, error, message, headers);
    }
    if (status >= 500) {
      return new InternalServerError(status, error, message, headers);
    }
    return new APIError(status, error, message, headers);
  }
}
class APIUserAbortError extends APIError {
  constructor({ message } = {}) {
    super(void 0, void 0, message || "Request was aborted.", void 0);
  }
}
class APIConnectionError extends APIError {
  constructor({ message, cause }) {
    super(void 0, void 0, message || "Connection error.", void 0);
    if (cause)
      this.cause = cause;
  }
}
class APIConnectionTimeoutError extends APIConnectionError {
  constructor({ message } = {}) {
    super({ message: message ?? "Request timed out." });
  }
}
class BadRequestError extends APIError {
}
class AuthenticationError extends APIError {
}
class PermissionDeniedError extends APIError {
}
class NotFoundError2 extends APIError {
}
class ConflictError extends APIError {
}
class UnprocessableEntityError extends APIError {
}
class RateLimitError extends APIError {
}
class InternalServerError extends APIError {
}
const startsWithSchemeRegexp = /^[a-z][a-z0-9+.-]*:/i;
const isAbsoluteURL = (url) => {
  return startsWithSchemeRegexp.test(url);
};
let isArray = (val) => (isArray = Array.isArray, isArray(val));
let isReadonlyArray = isArray;
function maybeObj(x) {
  if (typeof x !== "object") {
    return {};
  }
  return x ?? {};
}
function isEmptyObj(obj) {
  if (!obj)
    return true;
  for (const _k in obj)
    return false;
  return true;
}
function hasOwn(obj, key) {
  return Object.prototype.hasOwnProperty.call(obj, key);
}
const validatePositiveInteger = (name, n) => {
  if (typeof n !== "number" || !Number.isInteger(n)) {
    throw new AnthropicError(`${name} must be an integer`);
  }
  if (n < 0) {
    throw new AnthropicError(`${name} must be a positive integer`);
  }
  return n;
};
const safeJSON = (text) => {
  try {
    return JSON.parse(text);
  } catch (err) {
    return void 0;
  }
};
const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
const VERSION = "0.62.0";
const isRunningInBrowser = () => {
  return (
    // @ts-ignore
    typeof window !== "undefined" && // @ts-ignore
    typeof window.document !== "undefined" && // @ts-ignore
    typeof navigator !== "undefined"
  );
};
function getDetectedPlatform() {
  if (typeof Deno !== "undefined" && Deno.build != null) {
    return "deno";
  }
  if (typeof EdgeRuntime !== "undefined") {
    return "edge";
  }
  if (Object.prototype.toString.call(typeof globalThis.process !== "undefined" ? globalThis.process : 0) === "[object process]") {
    return "node";
  }
  return "unknown";
}
const getPlatformProperties = () => {
  const detectedPlatform = getDetectedPlatform();
  if (detectedPlatform === "deno") {
    return {
      "X-Stainless-Lang": "js",
      "X-Stainless-Package-Version": VERSION,
      "X-Stainless-OS": normalizePlatform(Deno.build.os),
      "X-Stainless-Arch": normalizeArch(Deno.build.arch),
      "X-Stainless-Runtime": "deno",
      "X-Stainless-Runtime-Version": typeof Deno.version === "string" ? Deno.version : Deno.version?.deno ?? "unknown"
    };
  }
  if (typeof EdgeRuntime !== "undefined") {
    return {
      "X-Stainless-Lang": "js",
      "X-Stainless-Package-Version": VERSION,
      "X-Stainless-OS": "Unknown",
      "X-Stainless-Arch": `other:${EdgeRuntime}`,
      "X-Stainless-Runtime": "edge",
      "X-Stainless-Runtime-Version": globalThis.process.version
    };
  }
  if (detectedPlatform === "node") {
    return {
      "X-Stainless-Lang": "js",
      "X-Stainless-Package-Version": VERSION,
      "X-Stainless-OS": normalizePlatform(globalThis.process.platform ?? "unknown"),
      "X-Stainless-Arch": normalizeArch(globalThis.process.arch ?? "unknown"),
      "X-Stainless-Runtime": "node",
      "X-Stainless-Runtime-Version": globalThis.process.version ?? "unknown"
    };
  }
  const browserInfo = getBrowserInfo();
  if (browserInfo) {
    return {
      "X-Stainless-Lang": "js",
      "X-Stainless-Package-Version": VERSION,
      "X-Stainless-OS": "Unknown",
      "X-Stainless-Arch": "unknown",
      "X-Stainless-Runtime": `browser:${browserInfo.browser}`,
      "X-Stainless-Runtime-Version": browserInfo.version
    };
  }
  return {
    "X-Stainless-Lang": "js",
    "X-Stainless-Package-Version": VERSION,
    "X-Stainless-OS": "Unknown",
    "X-Stainless-Arch": "unknown",
    "X-Stainless-Runtime": "unknown",
    "X-Stainless-Runtime-Version": "unknown"
  };
};
function getBrowserInfo() {
  if (typeof navigator === "undefined" || !navigator) {
    return null;
  }
  const browserPatterns = [
    { key: "edge", pattern: /Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/ },
    { key: "ie", pattern: /MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/ },
    { key: "ie", pattern: /Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/ },
    { key: "chrome", pattern: /Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/ },
    { key: "firefox", pattern: /Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/ },
    { key: "safari", pattern: /(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/ }
  ];
  for (const { key, pattern } of browserPatterns) {
    const match = pattern.exec(navigator.userAgent);
    if (match) {
      const major = match[1] || 0;
      const minor = match[2] || 0;
      const patch = match[3] || 0;
      return { browser: key, version: `${major}.${minor}.${patch}` };
    }
  }
  return null;
}
const normalizeArch = (arch) => {
  if (arch === "x32")
    return "x32";
  if (arch === "x86_64" || arch === "x64")
    return "x64";
  if (arch === "arm")
    return "arm";
  if (arch === "aarch64" || arch === "arm64")
    return "arm64";
  if (arch)
    return `other:${arch}`;
  return "unknown";
};
const normalizePlatform = (platform) => {
  platform = platform.toLowerCase();
  if (platform.includes("ios"))
    return "iOS";
  if (platform === "android")
    return "Android";
  if (platform === "darwin")
    return "MacOS";
  if (platform === "win32")
    return "Windows";
  if (platform === "freebsd")
    return "FreeBSD";
  if (platform === "openbsd")
    return "OpenBSD";
  if (platform === "linux")
    return "Linux";
  if (platform)
    return `Other:${platform}`;
  return "Unknown";
};
let _platformHeaders;
const getPlatformHeaders = () => {
  return _platformHeaders ?? (_platformHeaders = getPlatformProperties());
};
function getDefaultFetch() {
  if (typeof fetch !== "undefined") {
    return fetch;
  }
  throw new Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new Anthropic({ fetch })` or polyfill the global, `globalThis.fetch = fetch`");
}
function makeReadableStream(...args) {
  const ReadableStream = globalThis.ReadableStream;
  if (typeof ReadableStream === "undefined") {
    throw new Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");
  }
  return new ReadableStream(...args);
}
function ReadableStreamFrom(iterable) {
  let iter = Symbol.asyncIterator in iterable ? iterable[Symbol.asyncIterator]() : iterable[Symbol.iterator]();
  return makeReadableStream({
    start() {
    },
    async pull(controller) {
      const { done, value } = await iter.next();
      if (done) {
        controller.close();
      } else {
        controller.enqueue(value);
      }
    },
    async cancel() {
      await iter.return?.();
    }
  });
}
function ReadableStreamToAsyncIterable(stream) {
  if (stream[Symbol.asyncIterator])
    return stream;
  const reader = stream.getReader();
  return {
    async next() {
      try {
        const result = await reader.read();
        if (result?.done)
          reader.releaseLock();
        return result;
      } catch (e) {
        reader.releaseLock();
        throw e;
      }
    },
    async return() {
      const cancelPromise = reader.cancel();
      reader.releaseLock();
      await cancelPromise;
      return { done: true, value: void 0 };
    },
    [Symbol.asyncIterator]() {
      return this;
    }
  };
}
async function CancelReadableStream(stream) {
  if (stream === null || typeof stream !== "object")
    return;
  if (stream[Symbol.asyncIterator]) {
    await stream[Symbol.asyncIterator]().return?.();
    return;
  }
  const reader = stream.getReader();
  const cancelPromise = reader.cancel();
  reader.releaseLock();
  await cancelPromise;
}
const FallbackEncoder = ({ headers, body }) => {
  return {
    bodyHeaders: {
      "content-type": "application/json"
    },
    body: JSON.stringify(body)
  };
};
function concatBytes(buffers) {
  let length = 0;
  for (const buffer of buffers) {
    length += buffer.length;
  }
  const output = new Uint8Array(length);
  let index = 0;
  for (const buffer of buffers) {
    output.set(buffer, index);
    index += buffer.length;
  }
  return output;
}
let encodeUTF8_;
function encodeUTF8(str) {
  let encoder;
  return (encodeUTF8_ ?? (encoder = new globalThis.TextEncoder(), encodeUTF8_ = encoder.encode.bind(encoder)))(str);
}
let decodeUTF8_;
function decodeUTF8(bytes) {
  let decoder;
  return (decodeUTF8_ ?? (decoder = new globalThis.TextDecoder(), decodeUTF8_ = decoder.decode.bind(decoder)))(bytes);
}
var _LineDecoder_buffer, _LineDecoder_carriageReturnIndex;
class LineDecoder {
  constructor() {
    _LineDecoder_buffer.set(this, void 0);
    _LineDecoder_carriageReturnIndex.set(this, void 0);
    __classPrivateFieldSet(this, _LineDecoder_buffer, new Uint8Array());
    __classPrivateFieldSet(this, _LineDecoder_carriageReturnIndex, null);
  }
  decode(chunk) {
    if (chunk == null) {
      return [];
    }
    const binaryChunk = chunk instanceof ArrayBuffer ? new Uint8Array(chunk) : typeof chunk === "string" ? encodeUTF8(chunk) : chunk;
    __classPrivateFieldSet(this, _LineDecoder_buffer, concatBytes([__classPrivateFieldGet(this, _LineDecoder_buffer, "f"), binaryChunk]));
    const lines = [];
    let patternIndex;
    while ((patternIndex = findNewlineIndex(__classPrivateFieldGet(this, _LineDecoder_buffer, "f"), __classPrivateFieldGet(this, _LineDecoder_carriageReturnIndex, "f"))) != null) {
      if (patternIndex.carriage && __classPrivateFieldGet(this, _LineDecoder_carriageReturnIndex, "f") == null) {
        __classPrivateFieldSet(this, _LineDecoder_carriageReturnIndex, patternIndex.index);
        continue;
      }
      if (__classPrivateFieldGet(this, _LineDecoder_carriageReturnIndex, "f") != null && (patternIndex.index !== __classPrivateFieldGet(this, _LineDecoder_carriageReturnIndex, "f") + 1 || patternIndex.carriage)) {
        lines.push(decodeUTF8(__classPrivateFieldGet(this, _LineDecoder_buffer, "f").subarray(0, __classPrivateFieldGet(this, _LineDecoder_carriageReturnIndex, "f") - 1)));
        __classPrivateFieldSet(this, _LineDecoder_buffer, __classPrivateFieldGet(this, _LineDecoder_buffer, "f").subarray(__classPrivateFieldGet(this, _LineDecoder_carriageReturnIndex, "f")));
        __classPrivateFieldSet(this, _LineDecoder_carriageReturnIndex, null);
        continue;
      }
      const endIndex = __classPrivateFieldGet(this, _LineDecoder_carriageReturnIndex, "f") !== null ? patternIndex.preceding - 1 : patternIndex.preceding;
      const line = decodeUTF8(__classPrivateFieldGet(this, _LineDecoder_buffer, "f").subarray(0, endIndex));
      lines.push(line);
      __classPrivateFieldSet(this, _LineDecoder_buffer, __classPrivateFieldGet(this, _LineDecoder_buffer, "f").subarray(patternIndex.index));
      __classPrivateFieldSet(this, _LineDecoder_carriageReturnIndex, null);
    }
    return lines;
  }
  flush() {
    if (!__classPrivateFieldGet(this, _LineDecoder_buffer, "f").length) {
      return [];
    }
    return this.decode("\n");
  }
}
_LineDecoder_buffer = /* @__PURE__ */ new WeakMap(), _LineDecoder_carriageReturnIndex = /* @__PURE__ */ new WeakMap();
LineDecoder.NEWLINE_CHARS = /* @__PURE__ */ new Set(["\n", "\r"]);
LineDecoder.NEWLINE_REGEXP = /\r\n|[\n\r]/g;
function findNewlineIndex(buffer, startIndex) {
  const newline = 10;
  const carriage = 13;
  for (let i = startIndex ?? 0; i < buffer.length; i++) {
    if (buffer[i] === newline) {
      return { preceding: i, index: i + 1, carriage: false };
    }
    if (buffer[i] === carriage) {
      return { preceding: i, index: i + 1, carriage: true };
    }
  }
  return null;
}
function findDoubleNewlineIndex(buffer) {
  const newline = 10;
  const carriage = 13;
  for (let i = 0; i < buffer.length - 1; i++) {
    if (buffer[i] === newline && buffer[i + 1] === newline) {
      return i + 2;
    }
    if (buffer[i] === carriage && buffer[i + 1] === carriage) {
      return i + 2;
    }
    if (buffer[i] === carriage && buffer[i + 1] === newline && i + 3 < buffer.length && buffer[i + 2] === carriage && buffer[i + 3] === newline) {
      return i + 4;
    }
  }
  return -1;
}
const levelNumbers = {
  off: 0,
  error: 200,
  warn: 300,
  info: 400,
  debug: 500
};
const parseLogLevel = (maybeLevel, sourceName, client) => {
  if (!maybeLevel) {
    return void 0;
  }
  if (hasOwn(levelNumbers, maybeLevel)) {
    return maybeLevel;
  }
  loggerFor(client).warn(`${sourceName} was set to ${JSON.stringify(maybeLevel)}, expected one of ${JSON.stringify(Object.keys(levelNumbers))}`);
  return void 0;
};
function noop() {
}
function makeLogFn(fnLevel, logger2, logLevel) {
  if (!logger2 || levelNumbers[fnLevel] > levelNumbers[logLevel]) {
    return noop;
  } else {
    return logger2[fnLevel].bind(logger2);
  }
}
const noopLogger = {
  error: noop,
  warn: noop,
  info: noop,
  debug: noop
};
let cachedLoggers = /* @__PURE__ */ new WeakMap();
function loggerFor(client) {
  const logger2 = client.logger;
  const logLevel = client.logLevel ?? "off";
  if (!logger2) {
    return noopLogger;
  }
  const cachedLogger = cachedLoggers.get(logger2);
  if (cachedLogger && cachedLogger[0] === logLevel) {
    return cachedLogger[1];
  }
  const levelLogger = {
    error: makeLogFn("error", logger2, logLevel),
    warn: makeLogFn("warn", logger2, logLevel),
    info: makeLogFn("info", logger2, logLevel),
    debug: makeLogFn("debug", logger2, logLevel)
  };
  cachedLoggers.set(logger2, [logLevel, levelLogger]);
  return levelLogger;
}
const formatRequestDetails = (details) => {
  if (details.options) {
    details.options = { ...details.options };
    delete details.options["headers"];
  }
  if (details.headers) {
    details.headers = Object.fromEntries((details.headers instanceof Headers ? [...details.headers] : Object.entries(details.headers)).map(([name, value]) => [
      name,
      name.toLowerCase() === "x-api-key" || name.toLowerCase() === "authorization" || name.toLowerCase() === "cookie" || name.toLowerCase() === "set-cookie" ? "***" : value
    ]));
  }
  if ("retryOfRequestLogID" in details) {
    if (details.retryOfRequestLogID) {
      details.retryOf = details.retryOfRequestLogID;
    }
    delete details.retryOfRequestLogID;
  }
  return details;
};
var _Stream_client;
class Stream {
  constructor(iterator, controller, client) {
    this.iterator = iterator;
    _Stream_client.set(this, void 0);
    this.controller = controller;
    __classPrivateFieldSet(this, _Stream_client, client);
  }
  static fromSSEResponse(response, controller, client) {
    let consumed = false;
    const logger2 = client ? loggerFor(client) : console;
    async function* iterator() {
      if (consumed) {
        throw new AnthropicError("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");
      }
      consumed = true;
      let done = false;
      try {
        for await (const sse of _iterSSEMessages(response, controller)) {
          if (sse.event === "completion") {
            try {
              yield JSON.parse(sse.data);
            } catch (e) {
              logger2.error(`Could not parse message into JSON:`, sse.data);
              logger2.error(`From chunk:`, sse.raw);
              throw e;
            }
          }
          if (sse.event === "message_start" || sse.event === "message_delta" || sse.event === "message_stop" || sse.event === "content_block_start" || sse.event === "content_block_delta" || sse.event === "content_block_stop") {
            try {
              yield JSON.parse(sse.data);
            } catch (e) {
              logger2.error(`Could not parse message into JSON:`, sse.data);
              logger2.error(`From chunk:`, sse.raw);
              throw e;
            }
          }
          if (sse.event === "ping") {
            continue;
          }
          if (sse.event === "error") {
            throw new APIError(void 0, safeJSON(sse.data) ?? sse.data, void 0, response.headers);
          }
        }
        done = true;
      } catch (e) {
        if (isAbortError(e))
          return;
        throw e;
      } finally {
        if (!done)
          controller.abort();
      }
    }
    return new Stream(iterator, controller, client);
  }
  /**
   * Generates a Stream from a newline-separated ReadableStream
   * where each item is a JSON value.
   */
  static fromReadableStream(readableStream, controller, client) {
    let consumed = false;
    async function* iterLines() {
      const lineDecoder = new LineDecoder();
      const iter = ReadableStreamToAsyncIterable(readableStream);
      for await (const chunk of iter) {
        for (const line of lineDecoder.decode(chunk)) {
          yield line;
        }
      }
      for (const line of lineDecoder.flush()) {
        yield line;
      }
    }
    async function* iterator() {
      if (consumed) {
        throw new AnthropicError("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");
      }
      consumed = true;
      let done = false;
      try {
        for await (const line of iterLines()) {
          if (done)
            continue;
          if (line)
            yield JSON.parse(line);
        }
        done = true;
      } catch (e) {
        if (isAbortError(e))
          return;
        throw e;
      } finally {
        if (!done)
          controller.abort();
      }
    }
    return new Stream(iterator, controller, client);
  }
  [(_Stream_client = /* @__PURE__ */ new WeakMap(), Symbol.asyncIterator)]() {
    return this.iterator();
  }
  /**
   * Splits the stream into two streams which can be
   * independently read from at different speeds.
   */
  tee() {
    const left = [];
    const right = [];
    const iterator = this.iterator();
    const teeIterator = (queue) => {
      return {
        next: () => {
          if (queue.length === 0) {
            const result = iterator.next();
            left.push(result);
            right.push(result);
          }
          return queue.shift();
        }
      };
    };
    return [
      new Stream(() => teeIterator(left), this.controller, __classPrivateFieldGet(this, _Stream_client, "f")),
      new Stream(() => teeIterator(right), this.controller, __classPrivateFieldGet(this, _Stream_client, "f"))
    ];
  }
  /**
   * Converts this stream to a newline-separated ReadableStream of
   * JSON stringified values in the stream
   * which can be turned back into a Stream with `Stream.fromReadableStream()`.
   */
  toReadableStream() {
    const self = this;
    let iter;
    return makeReadableStream({
      async start() {
        iter = self[Symbol.asyncIterator]();
      },
      async pull(ctrl) {
        try {
          const { value, done } = await iter.next();
          if (done)
            return ctrl.close();
          const bytes = encodeUTF8(JSON.stringify(value) + "\n");
          ctrl.enqueue(bytes);
        } catch (err) {
          ctrl.error(err);
        }
      },
      async cancel() {
        await iter.return?.();
      }
    });
  }
}
async function* _iterSSEMessages(response, controller) {
  if (!response.body) {
    controller.abort();
    if (typeof globalThis.navigator !== "undefined" && globalThis.navigator.product === "ReactNative") {
      throw new AnthropicError(`The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api`);
    }
    throw new AnthropicError(`Attempted to iterate over a response with no body`);
  }
  const sseDecoder = new SSEDecoder();
  const lineDecoder = new LineDecoder();
  const iter = ReadableStreamToAsyncIterable(response.body);
  for await (const sseChunk of iterSSEChunks(iter)) {
    for (const line of lineDecoder.decode(sseChunk)) {
      const sse = sseDecoder.decode(line);
      if (sse)
        yield sse;
    }
  }
  for (const line of lineDecoder.flush()) {
    const sse = sseDecoder.decode(line);
    if (sse)
      yield sse;
  }
}
async function* iterSSEChunks(iterator) {
  let data = new Uint8Array();
  for await (const chunk of iterator) {
    if (chunk == null) {
      continue;
    }
    const binaryChunk = chunk instanceof ArrayBuffer ? new Uint8Array(chunk) : typeof chunk === "string" ? encodeUTF8(chunk) : chunk;
    let newData = new Uint8Array(data.length + binaryChunk.length);
    newData.set(data);
    newData.set(binaryChunk, data.length);
    data = newData;
    let patternIndex;
    while ((patternIndex = findDoubleNewlineIndex(data)) !== -1) {
      yield data.slice(0, patternIndex);
      data = data.slice(patternIndex);
    }
  }
  if (data.length > 0) {
    yield data;
  }
}
class SSEDecoder {
  constructor() {
    this.event = null;
    this.data = [];
    this.chunks = [];
  }
  decode(line) {
    if (line.endsWith("\r")) {
      line = line.substring(0, line.length - 1);
    }
    if (!line) {
      if (!this.event && !this.data.length)
        return null;
      const sse = {
        event: this.event,
        data: this.data.join("\n"),
        raw: this.chunks
      };
      this.event = null;
      this.data = [];
      this.chunks = [];
      return sse;
    }
    this.chunks.push(line);
    if (line.startsWith(":")) {
      return null;
    }
    let [fieldname, _, value] = partition(line, ":");
    if (value.startsWith(" ")) {
      value = value.substring(1);
    }
    if (fieldname === "event") {
      this.event = value;
    } else if (fieldname === "data") {
      this.data.push(value);
    }
    return null;
  }
}
function partition(str, delimiter) {
  const index = str.indexOf(delimiter);
  if (index !== -1) {
    return [str.substring(0, index), delimiter, str.substring(index + delimiter.length)];
  }
  return [str, "", ""];
}
async function defaultParseResponse(client, props) {
  const { response, requestLogID, retryOfRequestLogID, startTime } = props;
  const body = await (async () => {
    if (props.options.stream) {
      loggerFor(client).debug("response", response.status, response.url, response.headers, response.body);
      if (props.options.__streamClass) {
        return props.options.__streamClass.fromSSEResponse(response, props.controller);
      }
      return Stream.fromSSEResponse(response, props.controller);
    }
    if (response.status === 204) {
      return null;
    }
    if (props.options.__binaryResponse) {
      return response;
    }
    const contentType = response.headers.get("content-type");
    const mediaType = contentType?.split(";")[0]?.trim();
    const isJSON = mediaType?.includes("application/json") || mediaType?.endsWith("+json");
    if (isJSON) {
      const json = await response.json();
      return addRequestID(json, response);
    }
    const text = await response.text();
    return text;
  })();
  loggerFor(client).debug(`[${requestLogID}] response parsed`, formatRequestDetails({
    retryOfRequestLogID,
    url: response.url,
    status: response.status,
    body,
    durationMs: Date.now() - startTime
  }));
  return body;
}
function addRequestID(value, response) {
  if (!value || typeof value !== "object" || Array.isArray(value)) {
    return value;
  }
  return Object.defineProperty(value, "_request_id", {
    value: response.headers.get("request-id"),
    enumerable: false
  });
}
var _APIPromise_client;
class APIPromise extends Promise {
  constructor(client, responsePromise, parseResponse = defaultParseResponse) {
    super((resolve) => {
      resolve(null);
    });
    this.responsePromise = responsePromise;
    this.parseResponse = parseResponse;
    _APIPromise_client.set(this, void 0);
    __classPrivateFieldSet(this, _APIPromise_client, client);
  }
  _thenUnwrap(transform) {
    return new APIPromise(__classPrivateFieldGet(this, _APIPromise_client, "f"), this.responsePromise, async (client, props) => addRequestID(transform(await this.parseResponse(client, props), props), props.response));
  }
  /**
   * Gets the raw `Response` instance instead of parsing the response
   * data.
   *
   * If you want to parse the response body but still get the `Response`
   * instance, you can use {@link withResponse()}.
   *
   * 👋 Getting the wrong TypeScript type for `Response`?
   * Try setting `"moduleResolution": "NodeNext"` or add `"lib": ["DOM"]`
   * to your `tsconfig.json`.
   */
  asResponse() {
    return this.responsePromise.then((p) => p.response);
  }
  /**
   * Gets the parsed response data, the raw `Response` instance and the ID of the request,
   * returned via the `request-id` header which is useful for debugging requests and resporting
   * issues to Anthropic.
   *
   * If you just want to get the raw `Response` instance without parsing it,
   * you can use {@link asResponse()}.
   *
   * 👋 Getting the wrong TypeScript type for `Response`?
   * Try setting `"moduleResolution": "NodeNext"` or add `"lib": ["DOM"]`
   * to your `tsconfig.json`.
   */
  async withResponse() {
    const [data, response] = await Promise.all([this.parse(), this.asResponse()]);
    return { data, response, request_id: response.headers.get("request-id") };
  }
  parse() {
    if (!this.parsedPromise) {
      this.parsedPromise = this.responsePromise.then((data) => this.parseResponse(__classPrivateFieldGet(this, _APIPromise_client, "f"), data));
    }
    return this.parsedPromise;
  }
  then(onfulfilled, onrejected) {
    return this.parse().then(onfulfilled, onrejected);
  }
  catch(onrejected) {
    return this.parse().catch(onrejected);
  }
  finally(onfinally) {
    return this.parse().finally(onfinally);
  }
}
_APIPromise_client = /* @__PURE__ */ new WeakMap();
var _AbstractPage_client;
class AbstractPage {
  constructor(client, response, body, options) {
    _AbstractPage_client.set(this, void 0);
    __classPrivateFieldSet(this, _AbstractPage_client, client);
    this.options = options;
    this.response = response;
    this.body = body;
  }
  hasNextPage() {
    const items = this.getPaginatedItems();
    if (!items.length)
      return false;
    return this.nextPageRequestOptions() != null;
  }
  async getNextPage() {
    const nextOptions = this.nextPageRequestOptions();
    if (!nextOptions) {
      throw new AnthropicError("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");
    }
    return await __classPrivateFieldGet(this, _AbstractPage_client, "f").requestAPIList(this.constructor, nextOptions);
  }
  async *iterPages() {
    let page = this;
    yield page;
    while (page.hasNextPage()) {
      page = await page.getNextPage();
      yield page;
    }
  }
  async *[(_AbstractPage_client = /* @__PURE__ */ new WeakMap(), Symbol.asyncIterator)]() {
    for await (const page of this.iterPages()) {
      for (const item of page.getPaginatedItems()) {
        yield item;
      }
    }
  }
}
class PagePromise extends APIPromise {
  constructor(client, request, Page2) {
    super(client, request, async (client2, props) => new Page2(client2, props.response, await defaultParseResponse(client2, props), props.options));
  }
  /**
   * Allow auto-paginating iteration on an unawaited list call, eg:
   *
   *    for await (const item of client.items.list()) {
   *      console.log(item)
   *    }
   */
  async *[Symbol.asyncIterator]() {
    const page = await this;
    for await (const item of page) {
      yield item;
    }
  }
}
class Page extends AbstractPage {
  constructor(client, response, body, options) {
    super(client, response, body, options);
    this.data = body.data || [];
    this.has_more = body.has_more || false;
    this.first_id = body.first_id || null;
    this.last_id = body.last_id || null;
  }
  getPaginatedItems() {
    return this.data ?? [];
  }
  hasNextPage() {
    if (this.has_more === false) {
      return false;
    }
    return super.hasNextPage();
  }
  nextPageRequestOptions() {
    if (this.options.query?.["before_id"]) {
      const first_id = this.first_id;
      if (!first_id) {
        return null;
      }
      return {
        ...this.options,
        query: {
          ...maybeObj(this.options.query),
          before_id: first_id
        }
      };
    }
    const cursor = this.last_id;
    if (!cursor) {
      return null;
    }
    return {
      ...this.options,
      query: {
        ...maybeObj(this.options.query),
        after_id: cursor
      }
    };
  }
}
const checkFileSupport = () => {
  if (typeof File === "undefined") {
    const { process: process2 } = globalThis;
    const isOldNode = typeof process2?.versions?.node === "string" && parseInt(process2.versions.node.split(".")) < 20;
    throw new Error("`File` is not defined as a global, which is required for file uploads." + (isOldNode ? " Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`." : ""));
  }
};
function makeFile(fileBits, fileName, options) {
  checkFileSupport();
  return new File(fileBits, fileName ?? "unknown_file", options);
}
function getName(value) {
  return (typeof value === "object" && value !== null && ("name" in value && value.name && String(value.name) || "url" in value && value.url && String(value.url) || "filename" in value && value.filename && String(value.filename) || "path" in value && value.path && String(value.path)) || "").split(/[\\/]/).pop() || void 0;
}
const isAsyncIterable = (value) => value != null && typeof value === "object" && typeof value[Symbol.asyncIterator] === "function";
const multipartFormRequestOptions = async (opts, fetch2) => {
  return { ...opts, body: await createForm(opts.body, fetch2) };
};
const supportsFormDataMap = /* @__PURE__ */ new WeakMap();
function supportsFormData(fetchObject) {
  const fetch2 = typeof fetchObject === "function" ? fetchObject : fetchObject.fetch;
  const cached = supportsFormDataMap.get(fetch2);
  if (cached)
    return cached;
  const promise = (async () => {
    try {
      const FetchResponse = "Response" in fetch2 ? fetch2.Response : (await fetch2("data:,")).constructor;
      const data = new FormData();
      if (data.toString() === await new FetchResponse(data).text()) {
        return false;
      }
      return true;
    } catch {
      return true;
    }
  })();
  supportsFormDataMap.set(fetch2, promise);
  return promise;
}
const createForm = async (body, fetch2) => {
  if (!await supportsFormData(fetch2)) {
    throw new TypeError("The provided fetch function does not support file uploads with the current global FormData class.");
  }
  const form = new FormData();
  await Promise.all(Object.entries(body || {}).map(([key, value]) => addFormValue(form, key, value)));
  return form;
};
const isNamedBlob = (value) => value instanceof Blob && "name" in value;
const addFormValue = async (form, key, value) => {
  if (value === void 0)
    return;
  if (value == null) {
    throw new TypeError(`Received null for "${key}"; to pass null in FormData, you must use the string 'null'`);
  }
  if (typeof value === "string" || typeof value === "number" || typeof value === "boolean") {
    form.append(key, String(value));
  } else if (value instanceof Response) {
    let options = {};
    const contentType = value.headers.get("Content-Type");
    if (contentType) {
      options = { type: contentType };
    }
    form.append(key, makeFile([await value.blob()], getName(value), options));
  } else if (isAsyncIterable(value)) {
    form.append(key, makeFile([await new Response(ReadableStreamFrom(value)).blob()], getName(value)));
  } else if (isNamedBlob(value)) {
    form.append(key, makeFile([value], getName(value), { type: value.type }));
  } else if (Array.isArray(value)) {
    await Promise.all(value.map((entry) => addFormValue(form, key + "[]", entry)));
  } else if (typeof value === "object") {
    await Promise.all(Object.entries(value).map(([name, prop]) => addFormValue(form, `${key}[${name}]`, prop)));
  } else {
    throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${value} instead`);
  }
};
const isBlobLike = (value) => value != null && typeof value === "object" && typeof value.size === "number" && typeof value.type === "string" && typeof value.text === "function" && typeof value.slice === "function" && typeof value.arrayBuffer === "function";
const isFileLike = (value) => value != null && typeof value === "object" && typeof value.name === "string" && typeof value.lastModified === "number" && isBlobLike(value);
const isResponseLike = (value) => value != null && typeof value === "object" && typeof value.url === "string" && typeof value.blob === "function";
async function toFile(value, name, options) {
  checkFileSupport();
  value = await value;
  name || (name = getName(value));
  if (isFileLike(value)) {
    if (value instanceof File && name == null && options == null) {
      return value;
    }
    return makeFile([await value.arrayBuffer()], name ?? value.name, {
      type: value.type,
      lastModified: value.lastModified,
      ...options
    });
  }
  if (isResponseLike(value)) {
    const blob = await value.blob();
    name || (name = new URL(value.url).pathname.split(/[\\/]/).pop());
    return makeFile(await getBytes(blob), name, options);
  }
  const parts = await getBytes(value);
  if (!options?.type) {
    const type = parts.find((part) => typeof part === "object" && "type" in part && part.type);
    if (typeof type === "string") {
      options = { ...options, type };
    }
  }
  return makeFile(parts, name, options);
}
async function getBytes(value) {
  let parts = [];
  if (typeof value === "string" || ArrayBuffer.isView(value) || // includes Uint8Array, Buffer, etc.
  value instanceof ArrayBuffer) {
    parts.push(value);
  } else if (isBlobLike(value)) {
    parts.push(value instanceof Blob ? value : await value.arrayBuffer());
  } else if (isAsyncIterable(value)) {
    for await (const chunk of value) {
      parts.push(...await getBytes(chunk));
    }
  } else {
    const constructor = value?.constructor?.name;
    throw new Error(`Unexpected data type: ${typeof value}${constructor ? `; constructor: ${constructor}` : ""}${propsForError(value)}`);
  }
  return parts;
}
function propsForError(value) {
  if (typeof value !== "object" || value === null)
    return "";
  const props = Object.getOwnPropertyNames(value);
  return `; props: [${props.map((p) => `"${p}"`).join(", ")}]`;
}
class APIResource {
  constructor(client) {
    this._client = client;
  }
}
const brand_privateNullableHeaders = Symbol.for("brand.privateNullableHeaders");
function* iterateHeaders(headers) {
  if (!headers)
    return;
  if (brand_privateNullableHeaders in headers) {
    const { values, nulls } = headers;
    yield* values.entries();
    for (const name of nulls) {
      yield [name, null];
    }
    return;
  }
  let shouldClear = false;
  let iter;
  if (headers instanceof Headers) {
    iter = headers.entries();
  } else if (isReadonlyArray(headers)) {
    iter = headers;
  } else {
    shouldClear = true;
    iter = Object.entries(headers ?? {});
  }
  for (let row of iter) {
    const name = row[0];
    if (typeof name !== "string")
      throw new TypeError("expected header name to be a string");
    const values = isReadonlyArray(row[1]) ? row[1] : [row[1]];
    let didClear = false;
    for (const value of values) {
      if (value === void 0)
        continue;
      if (shouldClear && !didClear) {
        didClear = true;
        yield [name, null];
      }
      yield [name, value];
    }
  }
}
const buildHeaders = (newHeaders) => {
  const targetHeaders = new Headers();
  const nullHeaders = /* @__PURE__ */ new Set();
  for (const headers of newHeaders) {
    const seenHeaders = /* @__PURE__ */ new Set();
    for (const [name, value] of iterateHeaders(headers)) {
      const lowerName = name.toLowerCase();
      if (!seenHeaders.has(lowerName)) {
        targetHeaders.delete(name);
        seenHeaders.add(lowerName);
      }
      if (value === null) {
        targetHeaders.delete(name);
        nullHeaders.add(lowerName);
      } else {
        targetHeaders.append(name, value);
        nullHeaders.delete(lowerName);
      }
    }
  }
  return { [brand_privateNullableHeaders]: true, values: targetHeaders, nulls: nullHeaders };
};
function encodeURIPath(str) {
  return str.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g, encodeURIComponent);
}
const EMPTY = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.create(null));
const createPathTagFunction = (pathEncoder = encodeURIPath) => function path2(statics, ...params) {
  if (statics.length === 1)
    return statics[0];
  let postPath = false;
  const invalidSegments = [];
  const path3 = statics.reduce((previousValue, currentValue, index) => {
    if (/[?#]/.test(currentValue)) {
      postPath = true;
    }
    const value = params[index];
    let encoded = (postPath ? encodeURIComponent : pathEncoder)("" + value);
    if (index !== params.length && (value == null || typeof value === "object" && // handle values from other realms
    value.toString === Object.getPrototypeOf(Object.getPrototypeOf(value.hasOwnProperty ?? EMPTY) ?? EMPTY)?.toString)) {
      encoded = value + "";
      invalidSegments.push({
        start: previousValue.length + currentValue.length,
        length: encoded.length,
        error: `Value of type ${Object.prototype.toString.call(value).slice(8, -1)} is not a valid path parameter`
      });
    }
    return previousValue + currentValue + (index === params.length ? "" : encoded);
  }, "");
  const pathOnly = path3.split(/[?#]/, 1)[0];
  const invalidSegmentPattern = /(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;
  let match;
  while ((match = invalidSegmentPattern.exec(pathOnly)) !== null) {
    invalidSegments.push({
      start: match.index,
      length: match[0].length,
      error: `Value "${match[0]}" can't be safely passed as a path parameter`
    });
  }
  invalidSegments.sort((a, b) => a.start - b.start);
  if (invalidSegments.length > 0) {
    let lastEnd = 0;
    const underline = invalidSegments.reduce((acc, segment) => {
      const spaces = " ".repeat(segment.start - lastEnd);
      const arrows = "^".repeat(segment.length);
      lastEnd = segment.start + segment.length;
      return acc + spaces + arrows;
    }, "");
    throw new AnthropicError(`Path parameters result in path with invalid segments:
${invalidSegments.map((e) => e.error).join("\n")}
${path3}
${underline}`);
  }
  return path3;
};
const path = /* @__PURE__ */ createPathTagFunction(encodeURIPath);
class Files extends APIResource {
  /**
   * List Files
   *
   * @example
   * ```ts
   * // Automatically fetches more pages as needed.
   * for await (const fileMetadata of client.beta.files.list()) {
   *   // ...
   * }
   * ```
   */
  list(params = {}, options) {
    const { betas, ...query } = params ?? {};
    return this._client.getAPIList("/v1/files", Page, {
      query,
      ...options,
      headers: buildHeaders([
        { "anthropic-beta": [...betas ?? [], "files-api-2025-04-14"].toString() },
        options?.headers
      ])
    });
  }
  /**
   * Delete File
   *
   * @example
   * ```ts
   * const deletedFile = await client.beta.files.delete(
   *   'file_id',
   * );
   * ```
   */
  delete(fileID, params = {}, options) {
    const { betas } = params ?? {};
    return this._client.delete(path`/v1/files/${fileID}`, {
      ...options,
      headers: buildHeaders([
        { "anthropic-beta": [...betas ?? [], "files-api-2025-04-14"].toString() },
        options?.headers
      ])
    });
  }
  /**
   * Download File
   *
   * @example
   * ```ts
   * const response = await client.beta.files.download(
   *   'file_id',
   * );
   *
   * const content = await response.blob();
   * console.log(content);
   * ```
   */
  download(fileID, params = {}, options) {
    const { betas } = params ?? {};
    return this._client.get(path`/v1/files/${fileID}/content`, {
      ...options,
      headers: buildHeaders([
        {
          "anthropic-beta": [...betas ?? [], "files-api-2025-04-14"].toString(),
          Accept: "application/binary"
        },
        options?.headers
      ]),
      __binaryResponse: true
    });
  }
  /**
   * Get File Metadata
   *
   * @example
   * ```ts
   * const fileMetadata =
   *   await client.beta.files.retrieveMetadata('file_id');
   * ```
   */
  retrieveMetadata(fileID, params = {}, options) {
    const { betas } = params ?? {};
    return this._client.get(path`/v1/files/${fileID}`, {
      ...options,
      headers: buildHeaders([
        { "anthropic-beta": [...betas ?? [], "files-api-2025-04-14"].toString() },
        options?.headers
      ])
    });
  }
  /**
   * Upload File
   *
   * @example
   * ```ts
   * const fileMetadata = await client.beta.files.upload({
   *   file: fs.createReadStream('path/to/file'),
   * });
   * ```
   */
  upload(params, options) {
    const { betas, ...body } = params;
    return this._client.post("/v1/files", multipartFormRequestOptions({
      body,
      ...options,
      headers: buildHeaders([
        { "anthropic-beta": [...betas ?? [], "files-api-2025-04-14"].toString() },
        options?.headers
      ])
    }, this._client));
  }
}
let Models$1 = class Models extends APIResource {
  /**
   * Get a specific model.
   *
   * The Models API response can be used to determine information about a specific
   * model or resolve a model alias to a model ID.
   *
   * @example
   * ```ts
   * const betaModelInfo = await client.beta.models.retrieve(
   *   'model_id',
   * );
   * ```
   */
  retrieve(modelID, params = {}, options) {
    const { betas } = params ?? {};
    return this._client.get(path`/v1/models/${modelID}?beta=true`, {
      ...options,
      headers: buildHeaders([
        { ...betas?.toString() != null ? { "anthropic-beta": betas?.toString() } : void 0 },
        options?.headers
      ])
    });
  }
  /**
   * List available models.
   *
   * The Models API response can be used to determine which models are available for
   * use in the API. More recently released models are listed first.
   *
   * @example
   * ```ts
   * // Automatically fetches more pages as needed.
   * for await (const betaModelInfo of client.beta.models.list()) {
   *   // ...
   * }
   * ```
   */
  list(params = {}, options) {
    const { betas, ...query } = params ?? {};
    return this._client.getAPIList("/v1/models?beta=true", Page, {
      query,
      ...options,
      headers: buildHeaders([
        { ...betas?.toString() != null ? { "anthropic-beta": betas?.toString() } : void 0 },
        options?.headers
      ])
    });
  }
};
class JSONLDecoder {
  constructor(iterator, controller) {
    this.iterator = iterator;
    this.controller = controller;
  }
  async *decoder() {
    const lineDecoder = new LineDecoder();
    for await (const chunk of this.iterator) {
      for (const line of lineDecoder.decode(chunk)) {
        yield JSON.parse(line);
      }
    }
    for (const line of lineDecoder.flush()) {
      yield JSON.parse(line);
    }
  }
  [Symbol.asyncIterator]() {
    return this.decoder();
  }
  static fromResponse(response, controller) {
    if (!response.body) {
      controller.abort();
      if (typeof globalThis.navigator !== "undefined" && globalThis.navigator.product === "ReactNative") {
        throw new AnthropicError(`The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api`);
      }
      throw new AnthropicError(`Attempted to iterate over a response with no body`);
    }
    return new JSONLDecoder(ReadableStreamToAsyncIterable(response.body), controller);
  }
}
let Batches$1 = class Batches extends APIResource {
  /**
   * Send a batch of Message creation requests.
   *
   * The Message Batches API can be used to process multiple Messages API requests at
   * once. Once a Message Batch is created, it begins processing immediately. Batches
   * can take up to 24 hours to complete.
   *
   * Learn more about the Message Batches API in our
   * [user guide](/en/docs/build-with-claude/batch-processing)
   *
   * @example
   * ```ts
   * const betaMessageBatch =
   *   await client.beta.messages.batches.create({
   *     requests: [
   *       {
   *         custom_id: 'my-custom-id-1',
   *         params: {
   *           max_tokens: 1024,
   *           messages: [
   *             { content: 'Hello, world', role: 'user' },
   *           ],
   *           model: 'claude-sonnet-4-20250514',
   *         },
   *       },
   *     ],
   *   });
   * ```
   */
  create(params, options) {
    const { betas, ...body } = params;
    return this._client.post("/v1/messages/batches?beta=true", {
      body,
      ...options,
      headers: buildHeaders([
        { "anthropic-beta": [...betas ?? [], "message-batches-2024-09-24"].toString() },
        options?.headers
      ])
    });
  }
  /**
   * This endpoint is idempotent and can be used to poll for Message Batch
   * completion. To access the results of a Message Batch, make a request to the
   * `results_url` field in the response.
   *
   * Learn more about the Message Batches API in our
   * [user guide](/en/docs/build-with-claude/batch-processing)
   *
   * @example
   * ```ts
   * const betaMessageBatch =
   *   await client.beta.messages.batches.retrieve(
   *     'message_batch_id',
   *   );
   * ```
   */
  retrieve(messageBatchID, params = {}, options) {
    const { betas } = params ?? {};
    return this._client.get(path`/v1/messages/batches/${messageBatchID}?beta=true`, {
      ...options,
      headers: buildHeaders([
        { "anthropic-beta": [...betas ?? [], "message-batches-2024-09-24"].toString() },
        options?.headers
      ])
    });
  }
  /**
   * List all Message Batches within a Workspace. Most recently created batches are
   * returned first.
   *
   * Learn more about the Message Batches API in our
   * [user guide](/en/docs/build-with-claude/batch-processing)
   *
   * @example
   * ```ts
   * // Automatically fetches more pages as needed.
   * for await (const betaMessageBatch of client.beta.messages.batches.list()) {
   *   // ...
   * }
   * ```
   */
  list(params = {}, options) {
    const { betas, ...query } = params ?? {};
    return this._client.getAPIList("/v1/messages/batches?beta=true", Page, {
      query,
      ...options,
      headers: buildHeaders([
        { "anthropic-beta": [...betas ?? [], "message-batches-2024-09-24"].toString() },
        options?.headers
      ])
    });
  }
  /**
   * Delete a Message Batch.
   *
   * Message Batches can only be deleted once they've finished processing. If you'd
   * like to delete an in-progress batch, you must first cancel it.
   *
   * Learn more about the Message Batches API in our
   * [user guide](/en/docs/build-with-claude/batch-processing)
   *
   * @example
   * ```ts
   * const betaDeletedMessageBatch =
   *   await client.beta.messages.batches.delete(
   *     'message_batch_id',
   *   );
   * ```
   */
  delete(messageBatchID, params = {}, options) {
    const { betas } = params ?? {};
    return this._client.delete(path`/v1/messages/batches/${messageBatchID}?beta=true`, {
      ...options,
      headers: buildHeaders([
        { "anthropic-beta": [...betas ?? [], "message-batches-2024-09-24"].toString() },
        options?.headers
      ])
    });
  }
  /**
   * Batches may be canceled any time before processing ends. Once cancellation is
   * initiated, the batch enters a `canceling` state, at which time the system may
   * complete any in-progress, non-interruptible requests before finalizing
   * cancellation.
   *
   * The number of canceled requests is specified in `request_counts`. To determine
   * which requests were canceled, check the individual results within the batch.
   * Note that cancellation may not result in any canceled requests if they were
   * non-interruptible.
   *
   * Learn more about the Message Batches API in our
   * [user guide](/en/docs/build-with-claude/batch-processing)
   *
   * @example
   * ```ts
   * const betaMessageBatch =
   *   await client.beta.messages.batches.cancel(
   *     'message_batch_id',
   *   );
   * ```
   */
  cancel(messageBatchID, params = {}, options) {
    const { betas } = params ?? {};
    return this._client.post(path`/v1/messages/batches/${messageBatchID}/cancel?beta=true`, {
      ...options,
      headers: buildHeaders([
        { "anthropic-beta": [...betas ?? [], "message-batches-2024-09-24"].toString() },
        options?.headers
      ])
    });
  }
  /**
   * Streams the results of a Message Batch as a `.jsonl` file.
   *
   * Each line in the file is a JSON object containing the result of a single request
   * in the Message Batch. Results are not guaranteed to be in the same order as
   * requests. Use the `custom_id` field to match results to requests.
   *
   * Learn more about the Message Batches API in our
   * [user guide](/en/docs/build-with-claude/batch-processing)
   *
   * @example
   * ```ts
   * const betaMessageBatchIndividualResponse =
   *   await client.beta.messages.batches.results(
   *     'message_batch_id',
   *   );
   * ```
   */
  async results(messageBatchID, params = {}, options) {
    const batch = await this.retrieve(messageBatchID);
    if (!batch.results_url) {
      throw new AnthropicError(`No batch \`results_url\`; Has it finished processing? ${batch.processing_status} - ${batch.id}`);
    }
    const { betas } = params ?? {};
    return this._client.get(batch.results_url, {
      ...options,
      headers: buildHeaders([
        {
          "anthropic-beta": [...betas ?? [], "message-batches-2024-09-24"].toString(),
          Accept: "application/binary"
        },
        options?.headers
      ]),
      stream: true,
      __binaryResponse: true
    })._thenUnwrap((_, props) => JSONLDecoder.fromResponse(props.response, props.controller));
  }
};
const tokenize = (input) => {
  let current = 0;
  let tokens = [];
  while (current < input.length) {
    let char = input[current];
    if (char === "\\") {
      current++;
      continue;
    }
    if (char === "{") {
      tokens.push({
        type: "brace",
        value: "{"
      });
      current++;
      continue;
    }
    if (char === "}") {
      tokens.push({
        type: "brace",
        value: "}"
      });
      current++;
      continue;
    }
    if (char === "[") {
      tokens.push({
        type: "paren",
        value: "["
      });
      current++;
      continue;
    }
    if (char === "]") {
      tokens.push({
        type: "paren",
        value: "]"
      });
      current++;
      continue;
    }
    if (char === ":") {
      tokens.push({
        type: "separator",
        value: ":"
      });
      current++;
      continue;
    }
    if (char === ",") {
      tokens.push({
        type: "delimiter",
        value: ","
      });
      current++;
      continue;
    }
    if (char === '"') {
      let value = "";
      let danglingQuote = false;
      char = input[++current];
      while (char !== '"') {
        if (current === input.length) {
          danglingQuote = true;
          break;
        }
        if (char === "\\") {
          current++;
          if (current === input.length) {
            danglingQuote = true;
            break;
          }
          value += char + input[current];
          char = input[++current];
        } else {
          value += char;
          char = input[++current];
        }
      }
      char = input[++current];
      if (!danglingQuote) {
        tokens.push({
          type: "string",
          value
        });
      }
      continue;
    }
    let WHITESPACE = /\s/;
    if (char && WHITESPACE.test(char)) {
      current++;
      continue;
    }
    let NUMBERS = /[0-9]/;
    if (char && NUMBERS.test(char) || char === "-" || char === ".") {
      let value = "";
      if (char === "-") {
        value += char;
        char = input[++current];
      }
      while (char && NUMBERS.test(char) || char === ".") {
        value += char;
        char = input[++current];
      }
      tokens.push({
        type: "number",
        value
      });
      continue;
    }
    let LETTERS = /[a-z]/i;
    if (char && LETTERS.test(char)) {
      let value = "";
      while (char && LETTERS.test(char)) {
        if (current === input.length) {
          break;
        }
        value += char;
        char = input[++current];
      }
      if (value == "true" || value == "false" || value === "null") {
        tokens.push({
          type: "name",
          value
        });
      } else {
        current++;
        continue;
      }
      continue;
    }
    current++;
  }
  return tokens;
}, strip = (tokens) => {
  if (tokens.length === 0) {
    return tokens;
  }
  let lastToken = tokens[tokens.length - 1];
  switch (lastToken.type) {
    case "separator":
      tokens = tokens.slice(0, tokens.length - 1);
      return strip(tokens);
    case "number":
      let lastCharacterOfLastToken = lastToken.value[lastToken.value.length - 1];
      if (lastCharacterOfLastToken === "." || lastCharacterOfLastToken === "-") {
        tokens = tokens.slice(0, tokens.length - 1);
        return strip(tokens);
      }
    case "string":
      let tokenBeforeTheLastToken = tokens[tokens.length - 2];
      if (tokenBeforeTheLastToken?.type === "delimiter") {
        tokens = tokens.slice(0, tokens.length - 1);
        return strip(tokens);
      } else if (tokenBeforeTheLastToken?.type === "brace" && tokenBeforeTheLastToken.value === "{") {
        tokens = tokens.slice(0, tokens.length - 1);
        return strip(tokens);
      }
      break;
    case "delimiter":
      tokens = tokens.slice(0, tokens.length - 1);
      return strip(tokens);
  }
  return tokens;
}, unstrip = (tokens) => {
  let tail = [];
  tokens.map((token) => {
    if (token.type === "brace") {
      if (token.value === "{") {
        tail.push("}");
      } else {
        tail.splice(tail.lastIndexOf("}"), 1);
      }
    }
    if (token.type === "paren") {
      if (token.value === "[") {
        tail.push("]");
      } else {
        tail.splice(tail.lastIndexOf("]"), 1);
      }
    }
  });
  if (tail.length > 0) {
    tail.reverse().map((item) => {
      if (item === "}") {
        tokens.push({
          type: "brace",
          value: "}"
        });
      } else if (item === "]") {
        tokens.push({
          type: "paren",
          value: "]"
        });
      }
    });
  }
  return tokens;
}, generate = (tokens) => {
  let output = "";
  tokens.map((token) => {
    switch (token.type) {
      case "string":
        output += '"' + token.value + '"';
        break;
      default:
        output += token.value;
        break;
    }
  });
  return output;
}, partialParse = (input) => JSON.parse(generate(unstrip(strip(tokenize(input)))));
var _BetaMessageStream_instances, _BetaMessageStream_currentMessageSnapshot, _BetaMessageStream_connectedPromise, _BetaMessageStream_resolveConnectedPromise, _BetaMessageStream_rejectConnectedPromise, _BetaMessageStream_endPromise, _BetaMessageStream_resolveEndPromise, _BetaMessageStream_rejectEndPromise, _BetaMessageStream_listeners, _BetaMessageStream_ended, _BetaMessageStream_errored, _BetaMessageStream_aborted, _BetaMessageStream_catchingPromiseCreated, _BetaMessageStream_response, _BetaMessageStream_request_id, _BetaMessageStream_getFinalMessage, _BetaMessageStream_getFinalText, _BetaMessageStream_handleError, _BetaMessageStream_beginRequest, _BetaMessageStream_addStreamEvent, _BetaMessageStream_endRequest, _BetaMessageStream_accumulateMessage;
const JSON_BUF_PROPERTY$1 = "__json_buf";
function tracksToolInput$1(content) {
  return content.type === "tool_use" || content.type === "server_tool_use" || content.type === "mcp_tool_use";
}
class BetaMessageStream {
  constructor() {
    _BetaMessageStream_instances.add(this);
    this.messages = [];
    this.receivedMessages = [];
    _BetaMessageStream_currentMessageSnapshot.set(this, void 0);
    this.controller = new AbortController();
    _BetaMessageStream_connectedPromise.set(this, void 0);
    _BetaMessageStream_resolveConnectedPromise.set(this, () => {
    });
    _BetaMessageStream_rejectConnectedPromise.set(this, () => {
    });
    _BetaMessageStream_endPromise.set(this, void 0);
    _BetaMessageStream_resolveEndPromise.set(this, () => {
    });
    _BetaMessageStream_rejectEndPromise.set(this, () => {
    });
    _BetaMessageStream_listeners.set(this, {});
    _BetaMessageStream_ended.set(this, false);
    _BetaMessageStream_errored.set(this, false);
    _BetaMessageStream_aborted.set(this, false);
    _BetaMessageStream_catchingPromiseCreated.set(this, false);
    _BetaMessageStream_response.set(this, void 0);
    _BetaMessageStream_request_id.set(this, void 0);
    _BetaMessageStream_handleError.set(this, (error) => {
      __classPrivateFieldSet(this, _BetaMessageStream_errored, true);
      if (isAbortError(error)) {
        error = new APIUserAbortError();
      }
      if (error instanceof APIUserAbortError) {
        __classPrivateFieldSet(this, _BetaMessageStream_aborted, true);
        return this._emit("abort", error);
      }
      if (error instanceof AnthropicError) {
        return this._emit("error", error);
      }
      if (error instanceof Error) {
        const anthropicError = new AnthropicError(error.message);
        anthropicError.cause = error;
        return this._emit("error", anthropicError);
      }
      return this._emit("error", new AnthropicError(String(error)));
    });
    __classPrivateFieldSet(this, _BetaMessageStream_connectedPromise, new Promise((resolve, reject) => {
      __classPrivateFieldSet(this, _BetaMessageStream_resolveConnectedPromise, resolve, "f");
      __classPrivateFieldSet(this, _BetaMessageStream_rejectConnectedPromise, reject, "f");
    }));
    __classPrivateFieldSet(this, _BetaMessageStream_endPromise, new Promise((resolve, reject) => {
      __classPrivateFieldSet(this, _BetaMessageStream_resolveEndPromise, resolve, "f");
      __classPrivateFieldSet(this, _BetaMessageStream_rejectEndPromise, reject, "f");
    }));
    __classPrivateFieldGet(this, _BetaMessageStream_connectedPromise, "f").catch(() => {
    });
    __classPrivateFieldGet(this, _BetaMessageStream_endPromise, "f").catch(() => {
    });
  }
  get response() {
    return __classPrivateFieldGet(this, _BetaMessageStream_response, "f");
  }
  get request_id() {
    return __classPrivateFieldGet(this, _BetaMessageStream_request_id, "f");
  }
  /**
   * Returns the `MessageStream` data, the raw `Response` instance and the ID of the request,
   * returned vie the `request-id` header which is useful for debugging requests and resporting
   * issues to Anthropic.
   *
   * This is the same as the `APIPromise.withResponse()` method.
   *
   * This method will raise an error if you created the stream using `MessageStream.fromReadableStream`
   * as no `Response` is available.
   */
  async withResponse() {
    const response = await __classPrivateFieldGet(this, _BetaMessageStream_connectedPromise, "f");
    if (!response) {
      throw new Error("Could not resolve a `Response` object");
    }
    return {
      data: this,
      response,
      request_id: response.headers.get("request-id")
    };
  }
  /**
   * Intended for use on the frontend, consuming a stream produced with
   * `.toReadableStream()` on the backend.
   *
   * Note that messages sent to the model do not appear in `.on('message')`
   * in this context.
   */
  static fromReadableStream(stream) {
    const runner = new BetaMessageStream();
    runner._run(() => runner._fromReadableStream(stream));
    return runner;
  }
  static createMessage(messages, params, options) {
    const runner = new BetaMessageStream();
    for (const message of params.messages) {
      runner._addMessageParam(message);
    }
    runner._run(() => runner._createMessage(messages, { ...params, stream: true }, { ...options, headers: { ...options?.headers, "X-Stainless-Helper-Method": "stream" } }));
    return runner;
  }
  _run(executor) {
    executor().then(() => {
      this._emitFinal();
      this._emit("end");
    }, __classPrivateFieldGet(this, _BetaMessageStream_handleError, "f"));
  }
  _addMessageParam(message) {
    this.messages.push(message);
  }
  _addMessage(message, emit = true) {
    this.receivedMessages.push(message);
    if (emit) {
      this._emit("message", message);
    }
  }
  async _createMessage(messages, params, options) {
    const signal = options?.signal;
    let abortHandler;
    if (signal) {
      if (signal.aborted)
        this.controller.abort();
      abortHandler = this.controller.abort.bind(this.controller);
      signal.addEventListener("abort", abortHandler);
    }
    try {
      __classPrivateFieldGet(this, _BetaMessageStream_instances, "m", _BetaMessageStream_beginRequest).call(this);
      const { response, data: stream } = await messages.create({ ...params, stream: true }, { ...options, signal: this.controller.signal }).withResponse();
      this._connected(response);
      for await (const event of stream) {
        __classPrivateFieldGet(this, _BetaMessageStream_instances, "m", _BetaMessageStream_addStreamEvent).call(this, event);
      }
      if (stream.controller.signal?.aborted) {
        throw new APIUserAbortError();
      }
      __classPrivateFieldGet(this, _BetaMessageStream_instances, "m", _BetaMessageStream_endRequest).call(this);
    } finally {
      if (signal && abortHandler) {
        signal.removeEventListener("abort", abortHandler);
      }
    }
  }
  _connected(response) {
    if (this.ended)
      return;
    __classPrivateFieldSet(this, _BetaMessageStream_response, response);
    __classPrivateFieldSet(this, _BetaMessageStream_request_id, response?.headers.get("request-id"));
    __classPrivateFieldGet(this, _BetaMessageStream_resolveConnectedPromise, "f").call(this, response);
    this._emit("connect");
  }
  get ended() {
    return __classPrivateFieldGet(this, _BetaMessageStream_ended, "f");
  }
  get errored() {
    return __classPrivateFieldGet(this, _BetaMessageStream_errored, "f");
  }
  get aborted() {
    return __classPrivateFieldGet(this, _BetaMessageStream_aborted, "f");
  }
  abort() {
    this.controller.abort();
  }
  /**
   * Adds the listener function to the end of the listeners array for the event.
   * No checks are made to see if the listener has already been added. Multiple calls passing
   * the same combination of event and listener will result in the listener being added, and
   * called, multiple times.
   * @returns this MessageStream, so that calls can be chained
   */
  on(event, listener) {
    const listeners = __classPrivateFieldGet(this, _BetaMessageStream_listeners, "f")[event] || (__classPrivateFieldGet(this, _BetaMessageStream_listeners, "f")[event] = []);
    listeners.push({ listener });
    return this;
  }
  /**
   * Removes the specified listener from the listener array for the event.
   * off() will remove, at most, one instance of a listener from the listener array. If any single
   * listener has been added multiple times to the listener array for the specified event, then
   * off() must be called multiple times to remove each instance.
   * @returns this MessageStream, so that calls can be chained
   */
  off(event, listener) {
    const listeners = __classPrivateFieldGet(this, _BetaMessageStream_listeners, "f")[event];
    if (!listeners)
      return this;
    const index = listeners.findIndex((l) => l.listener === listener);
    if (index >= 0)
      listeners.splice(index, 1);
    return this;
  }
  /**
   * Adds a one-time listener function for the event. The next time the event is triggered,
   * this listener is removed and then invoked.
   * @returns this MessageStream, so that calls can be chained
   */
  once(event, listener) {
    const listeners = __classPrivateFieldGet(this, _BetaMessageStream_listeners, "f")[event] || (__classPrivateFieldGet(this, _BetaMessageStream_listeners, "f")[event] = []);
    listeners.push({ listener, once: true });
    return this;
  }
  /**
   * This is similar to `.once()`, but returns a Promise that resolves the next time
   * the event is triggered, instead of calling a listener callback.
   * @returns a Promise that resolves the next time given event is triggered,
   * or rejects if an error is emitted.  (If you request the 'error' event,
   * returns a promise that resolves with the error).
   *
   * Example:
   *
   *   const message = await stream.emitted('message') // rejects if the stream errors
   */
  emitted(event) {
    return new Promise((resolve, reject) => {
      __classPrivateFieldSet(this, _BetaMessageStream_catchingPromiseCreated, true);
      if (event !== "error")
        this.once("error", reject);
      this.once(event, resolve);
    });
  }
  async done() {
    __classPrivateFieldSet(this, _BetaMessageStream_catchingPromiseCreated, true);
    await __classPrivateFieldGet(this, _BetaMessageStream_endPromise, "f");
  }
  get currentMessage() {
    return __classPrivateFieldGet(this, _BetaMessageStream_currentMessageSnapshot, "f");
  }
  /**
   * @returns a promise that resolves with the the final assistant Message response,
   * or rejects if an error occurred or the stream ended prematurely without producing a Message.
   */
  async finalMessage() {
    await this.done();
    return __classPrivateFieldGet(this, _BetaMessageStream_instances, "m", _BetaMessageStream_getFinalMessage).call(this);
  }
  /**
   * @returns a promise that resolves with the the final assistant Message's text response, concatenated
   * together if there are more than one text blocks.
   * Rejects if an error occurred or the stream ended prematurely without producing a Message.
   */
  async finalText() {
    await this.done();
    return __classPrivateFieldGet(this, _BetaMessageStream_instances, "m", _BetaMessageStream_getFinalText).call(this);
  }
  _emit(event, ...args) {
    if (__classPrivateFieldGet(this, _BetaMessageStream_ended, "f"))
      return;
    if (event === "end") {
      __classPrivateFieldSet(this, _BetaMessageStream_ended, true);
      __classPrivateFieldGet(this, _BetaMessageStream_resolveEndPromise, "f").call(this);
    }
    const listeners = __classPrivateFieldGet(this, _BetaMessageStream_listeners, "f")[event];
    if (listeners) {
      __classPrivateFieldGet(this, _BetaMessageStream_listeners, "f")[event] = listeners.filter((l) => !l.once);
      listeners.forEach(({ listener }) => listener(...args));
    }
    if (event === "abort") {
      const error = args[0];
      if (!__classPrivateFieldGet(this, _BetaMessageStream_catchingPromiseCreated, "f") && !listeners?.length) {
        Promise.reject(error);
      }
      __classPrivateFieldGet(this, _BetaMessageStream_rejectConnectedPromise, "f").call(this, error);
      __classPrivateFieldGet(this, _BetaMessageStream_rejectEndPromise, "f").call(this, error);
      this._emit("end");
      return;
    }
    if (event === "error") {
      const error = args[0];
      if (!__classPrivateFieldGet(this, _BetaMessageStream_catchingPromiseCreated, "f") && !listeners?.length) {
        Promise.reject(error);
      }
      __classPrivateFieldGet(this, _BetaMessageStream_rejectConnectedPromise, "f").call(this, error);
      __classPrivateFieldGet(this, _BetaMessageStream_rejectEndPromise, "f").call(this, error);
      this._emit("end");
    }
  }
  _emitFinal() {
    const finalMessage = this.receivedMessages.at(-1);
    if (finalMessage) {
      this._emit("finalMessage", __classPrivateFieldGet(this, _BetaMessageStream_instances, "m", _BetaMessageStream_getFinalMessage).call(this));
    }
  }
  async _fromReadableStream(readableStream, options) {
    const signal = options?.signal;
    let abortHandler;
    if (signal) {
      if (signal.aborted)
        this.controller.abort();
      abortHandler = this.controller.abort.bind(this.controller);
      signal.addEventListener("abort", abortHandler);
    }
    try {
      __classPrivateFieldGet(this, _BetaMessageStream_instances, "m", _BetaMessageStream_beginRequest).call(this);
      this._connected(null);
      const stream = Stream.fromReadableStream(readableStream, this.controller);
      for await (const event of stream) {
        __classPrivateFieldGet(this, _BetaMessageStream_instances, "m", _BetaMessageStream_addStreamEvent).call(this, event);
      }
      if (stream.controller.signal?.aborted) {
        throw new APIUserAbortError();
      }
      __classPrivateFieldGet(this, _BetaMessageStream_instances, "m", _BetaMessageStream_endRequest).call(this);
    } finally {
      if (signal && abortHandler) {
        signal.removeEventListener("abort", abortHandler);
      }
    }
  }
  [(_BetaMessageStream_currentMessageSnapshot = /* @__PURE__ */ new WeakMap(), _BetaMessageStream_connectedPromise = /* @__PURE__ */ new WeakMap(), _BetaMessageStream_resolveConnectedPromise = /* @__PURE__ */ new WeakMap(), _BetaMessageStream_rejectConnectedPromise = /* @__PURE__ */ new WeakMap(), _BetaMessageStream_endPromise = /* @__PURE__ */ new WeakMap(), _BetaMessageStream_resolveEndPromise = /* @__PURE__ */ new WeakMap(), _BetaMessageStream_rejectEndPromise = /* @__PURE__ */ new WeakMap(), _BetaMessageStream_listeners = /* @__PURE__ */ new WeakMap(), _BetaMessageStream_ended = /* @__PURE__ */ new WeakMap(), _BetaMessageStream_errored = /* @__PURE__ */ new WeakMap(), _BetaMessageStream_aborted = /* @__PURE__ */ new WeakMap(), _BetaMessageStream_catchingPromiseCreated = /* @__PURE__ */ new WeakMap(), _BetaMessageStream_response = /* @__PURE__ */ new WeakMap(), _BetaMessageStream_request_id = /* @__PURE__ */ new WeakMap(), _BetaMessageStream_handleError = /* @__PURE__ */ new WeakMap(), _BetaMessageStream_instances = /* @__PURE__ */ new WeakSet(), _BetaMessageStream_getFinalMessage = function _BetaMessageStream_getFinalMessage2() {
    if (this.receivedMessages.length === 0) {
      throw new AnthropicError("stream ended without producing a Message with role=assistant");
    }
    return this.receivedMessages.at(-1);
  }, _BetaMessageStream_getFinalText = function _BetaMessageStream_getFinalText2() {
    if (this.receivedMessages.length === 0) {
      throw new AnthropicError("stream ended without producing a Message with role=assistant");
    }
    const textBlocks = this.receivedMessages.at(-1).content.filter((block) => block.type === "text").map((block) => block.text);
    if (textBlocks.length === 0) {
      throw new AnthropicError("stream ended without producing a content block with type=text");
    }
    return textBlocks.join(" ");
  }, _BetaMessageStream_beginRequest = function _BetaMessageStream_beginRequest2() {
    if (this.ended)
      return;
    __classPrivateFieldSet(this, _BetaMessageStream_currentMessageSnapshot, void 0);
  }, _BetaMessageStream_addStreamEvent = function _BetaMessageStream_addStreamEvent2(event) {
    if (this.ended)
      return;
    const messageSnapshot = __classPrivateFieldGet(this, _BetaMessageStream_instances, "m", _BetaMessageStream_accumulateMessage).call(this, event);
    this._emit("streamEvent", event, messageSnapshot);
    switch (event.type) {
      case "content_block_delta": {
        const content = messageSnapshot.content.at(-1);
        switch (event.delta.type) {
          case "text_delta": {
            if (content.type === "text") {
              this._emit("text", event.delta.text, content.text || "");
            }
            break;
          }
          case "citations_delta": {
            if (content.type === "text") {
              this._emit("citation", event.delta.citation, content.citations ?? []);
            }
            break;
          }
          case "input_json_delta": {
            if (tracksToolInput$1(content) && content.input) {
              this._emit("inputJson", event.delta.partial_json, content.input);
            }
            break;
          }
          case "thinking_delta": {
            if (content.type === "thinking") {
              this._emit("thinking", event.delta.thinking, content.thinking);
            }
            break;
          }
          case "signature_delta": {
            if (content.type === "thinking") {
              this._emit("signature", content.signature);
            }
            break;
          }
          default:
            checkNever$1(event.delta);
        }
        break;
      }
      case "message_stop": {
        this._addMessageParam(messageSnapshot);
        this._addMessage(messageSnapshot, true);
        break;
      }
      case "content_block_stop": {
        this._emit("contentBlock", messageSnapshot.content.at(-1));
        break;
      }
      case "message_start": {
        __classPrivateFieldSet(this, _BetaMessageStream_currentMessageSnapshot, messageSnapshot);
        break;
      }
    }
  }, _BetaMessageStream_endRequest = function _BetaMessageStream_endRequest2() {
    if (this.ended) {
      throw new AnthropicError(`stream has ended, this shouldn't happen`);
    }
    const snapshot = __classPrivateFieldGet(this, _BetaMessageStream_currentMessageSnapshot, "f");
    if (!snapshot) {
      throw new AnthropicError(`request ended without sending any chunks`);
    }
    __classPrivateFieldSet(this, _BetaMessageStream_currentMessageSnapshot, void 0);
    return snapshot;
  }, _BetaMessageStream_accumulateMessage = function _BetaMessageStream_accumulateMessage2(event) {
    let snapshot = __classPrivateFieldGet(this, _BetaMessageStream_currentMessageSnapshot, "f");
    if (event.type === "message_start") {
      if (snapshot) {
        throw new AnthropicError(`Unexpected event order, got ${event.type} before receiving "message_stop"`);
      }
      return event.message;
    }
    if (!snapshot) {
      throw new AnthropicError(`Unexpected event order, got ${event.type} before "message_start"`);
    }
    switch (event.type) {
      case "message_stop":
        return snapshot;
      case "message_delta":
        snapshot.container = event.delta.container;
        snapshot.stop_reason = event.delta.stop_reason;
        snapshot.stop_sequence = event.delta.stop_sequence;
        snapshot.usage.output_tokens = event.usage.output_tokens;
        if (event.usage.input_tokens != null) {
          snapshot.usage.input_tokens = event.usage.input_tokens;
        }
        if (event.usage.cache_creation_input_tokens != null) {
          snapshot.usage.cache_creation_input_tokens = event.usage.cache_creation_input_tokens;
        }
        if (event.usage.cache_read_input_tokens != null) {
          snapshot.usage.cache_read_input_tokens = event.usage.cache_read_input_tokens;
        }
        if (event.usage.server_tool_use != null) {
          snapshot.usage.server_tool_use = event.usage.server_tool_use;
        }
        return snapshot;
      case "content_block_start":
        snapshot.content.push(event.content_block);
        return snapshot;
      case "content_block_delta": {
        const snapshotContent = snapshot.content.at(event.index);
        switch (event.delta.type) {
          case "text_delta": {
            if (snapshotContent?.type === "text") {
              snapshot.content[event.index] = {
                ...snapshotContent,
                text: (snapshotContent.text || "") + event.delta.text
              };
            }
            break;
          }
          case "citations_delta": {
            if (snapshotContent?.type === "text") {
              snapshot.content[event.index] = {
                ...snapshotContent,
                citations: [...snapshotContent.citations ?? [], event.delta.citation]
              };
            }
            break;
          }
          case "input_json_delta": {
            if (snapshotContent && tracksToolInput$1(snapshotContent)) {
              let jsonBuf = snapshotContent[JSON_BUF_PROPERTY$1] || "";
              jsonBuf += event.delta.partial_json;
              const newContent = { ...snapshotContent };
              Object.defineProperty(newContent, JSON_BUF_PROPERTY$1, {
                value: jsonBuf,
                enumerable: false,
                writable: true
              });
              if (jsonBuf) {
                try {
                  newContent.input = partialParse(jsonBuf);
                } catch (err) {
                  const error = new AnthropicError(`Unable to parse tool parameter JSON from model. Please retry your request or adjust your prompt. Error: ${err}. JSON: ${jsonBuf}`);
                  __classPrivateFieldGet(this, _BetaMessageStream_handleError, "f").call(this, error);
                }
              }
              snapshot.content[event.index] = newContent;
            }
            break;
          }
          case "thinking_delta": {
            if (snapshotContent?.type === "thinking") {
              snapshot.content[event.index] = {
                ...snapshotContent,
                thinking: snapshotContent.thinking + event.delta.thinking
              };
            }
            break;
          }
          case "signature_delta": {
            if (snapshotContent?.type === "thinking") {
              snapshot.content[event.index] = {
                ...snapshotContent,
                signature: event.delta.signature
              };
            }
            break;
          }
          default:
            checkNever$1(event.delta);
        }
        return snapshot;
      }
      case "content_block_stop":
        return snapshot;
    }
  }, Symbol.asyncIterator)]() {
    const pushQueue = [];
    const readQueue = [];
    let done = false;
    this.on("streamEvent", (event) => {
      const reader = readQueue.shift();
      if (reader) {
        reader.resolve(event);
      } else {
        pushQueue.push(event);
      }
    });
    this.on("end", () => {
      done = true;
      for (const reader of readQueue) {
        reader.resolve(void 0);
      }
      readQueue.length = 0;
    });
    this.on("abort", (err) => {
      done = true;
      for (const reader of readQueue) {
        reader.reject(err);
      }
      readQueue.length = 0;
    });
    this.on("error", (err) => {
      done = true;
      for (const reader of readQueue) {
        reader.reject(err);
      }
      readQueue.length = 0;
    });
    return {
      next: async () => {
        if (!pushQueue.length) {
          if (done) {
            return { value: void 0, done: true };
          }
          return new Promise((resolve, reject) => readQueue.push({ resolve, reject })).then((chunk2) => chunk2 ? { value: chunk2, done: false } : { value: void 0, done: true });
        }
        const chunk = pushQueue.shift();
        return { value: chunk, done: false };
      },
      return: async () => {
        this.abort();
        return { value: void 0, done: true };
      }
    };
  }
  toReadableStream() {
    const stream = new Stream(this[Symbol.asyncIterator].bind(this), this.controller);
    return stream.toReadableStream();
  }
}
function checkNever$1(x) {
}
const MODEL_NONSTREAMING_TOKENS = {
  "claude-opus-4-20250514": 8192,
  "claude-opus-4-0": 8192,
  "claude-4-opus-20250514": 8192,
  "anthropic.claude-opus-4-20250514-v1:0": 8192,
  "claude-opus-4@20250514": 8192,
  "claude-opus-4-1-20250805": 8192,
  "anthropic.claude-opus-4-1-20250805-v1:0": 8192,
  "claude-opus-4-1@20250805": 8192
};
const DEPRECATED_MODELS$1 = {
  "claude-1.3": "November 6th, 2024",
  "claude-1.3-100k": "November 6th, 2024",
  "claude-instant-1.1": "November 6th, 2024",
  "claude-instant-1.1-100k": "November 6th, 2024",
  "claude-instant-1.2": "November 6th, 2024",
  "claude-3-sonnet-20240229": "July 21st, 2025",
  "claude-3-opus-20240229": "January 5th, 2026",
  "claude-2.1": "July 21st, 2025",
  "claude-2.0": "July 21st, 2025",
  "claude-3-5-sonnet-20241022": "October 22, 2025",
  "claude-3-5-sonnet-20240620": "October 22, 2025"
};
let Messages$1 = class Messages extends APIResource {
  constructor() {
    super(...arguments);
    this.batches = new Batches$1(this._client);
  }
  create(params, options) {
    const { betas, ...body } = params;
    if (body.model in DEPRECATED_MODELS$1) {
      console.warn(`The model '${body.model}' is deprecated and will reach end-of-life on ${DEPRECATED_MODELS$1[body.model]}
Please migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);
    }
    let timeout = this._client._options.timeout;
    if (!body.stream && timeout == null) {
      const maxNonstreamingTokens = MODEL_NONSTREAMING_TOKENS[body.model] ?? void 0;
      timeout = this._client.calculateNonstreamingTimeout(body.max_tokens, maxNonstreamingTokens);
    }
    return this._client.post("/v1/messages?beta=true", {
      body,
      timeout: timeout ?? 6e5,
      ...options,
      headers: buildHeaders([
        { ...betas?.toString() != null ? { "anthropic-beta": betas?.toString() } : void 0 },
        options?.headers
      ]),
      stream: params.stream ?? false
    });
  }
  /**
   * Create a Message stream
   */
  stream(body, options) {
    return BetaMessageStream.createMessage(this, body, options);
  }
  /**
   * Count the number of tokens in a Message.
   *
   * The Token Count API can be used to count the number of tokens in a Message,
   * including tools, images, and documents, without creating it.
   *
   * Learn more about token counting in our
   * [user guide](/en/docs/build-with-claude/token-counting)
   *
   * @example
   * ```ts
   * const betaMessageTokensCount =
   *   await client.beta.messages.countTokens({
   *     messages: [{ content: 'string', role: 'user' }],
   *     model: 'claude-3-7-sonnet-latest',
   *   });
   * ```
   */
  countTokens(params, options) {
    const { betas, ...body } = params;
    return this._client.post("/v1/messages/count_tokens?beta=true", {
      body,
      ...options,
      headers: buildHeaders([
        { "anthropic-beta": [...betas ?? [], "token-counting-2024-11-01"].toString() },
        options?.headers
      ])
    });
  }
};
Messages$1.Batches = Batches$1;
class Beta extends APIResource {
  constructor() {
    super(...arguments);
    this.models = new Models$1(this._client);
    this.messages = new Messages$1(this._client);
    this.files = new Files(this._client);
  }
}
Beta.Models = Models$1;
Beta.Messages = Messages$1;
Beta.Files = Files;
class Completions extends APIResource {
  create(params, options) {
    const { betas, ...body } = params;
    return this._client.post("/v1/complete", {
      body,
      timeout: this._client._options.timeout ?? 6e5,
      ...options,
      headers: buildHeaders([
        { ...betas?.toString() != null ? { "anthropic-beta": betas?.toString() } : void 0 },
        options?.headers
      ]),
      stream: params.stream ?? false
    });
  }
}
var _MessageStream_instances, _MessageStream_currentMessageSnapshot, _MessageStream_connectedPromise, _MessageStream_resolveConnectedPromise, _MessageStream_rejectConnectedPromise, _MessageStream_endPromise, _MessageStream_resolveEndPromise, _MessageStream_rejectEndPromise, _MessageStream_listeners, _MessageStream_ended, _MessageStream_errored, _MessageStream_aborted, _MessageStream_catchingPromiseCreated, _MessageStream_response, _MessageStream_request_id, _MessageStream_getFinalMessage, _MessageStream_getFinalText, _MessageStream_handleError, _MessageStream_beginRequest, _MessageStream_addStreamEvent, _MessageStream_endRequest, _MessageStream_accumulateMessage;
const JSON_BUF_PROPERTY = "__json_buf";
function tracksToolInput(content) {
  return content.type === "tool_use" || content.type === "server_tool_use";
}
class MessageStream {
  constructor() {
    _MessageStream_instances.add(this);
    this.messages = [];
    this.receivedMessages = [];
    _MessageStream_currentMessageSnapshot.set(this, void 0);
    this.controller = new AbortController();
    _MessageStream_connectedPromise.set(this, void 0);
    _MessageStream_resolveConnectedPromise.set(this, () => {
    });
    _MessageStream_rejectConnectedPromise.set(this, () => {
    });
    _MessageStream_endPromise.set(this, void 0);
    _MessageStream_resolveEndPromise.set(this, () => {
    });
    _MessageStream_rejectEndPromise.set(this, () => {
    });
    _MessageStream_listeners.set(this, {});
    _MessageStream_ended.set(this, false);
    _MessageStream_errored.set(this, false);
    _MessageStream_aborted.set(this, false);
    _MessageStream_catchingPromiseCreated.set(this, false);
    _MessageStream_response.set(this, void 0);
    _MessageStream_request_id.set(this, void 0);
    _MessageStream_handleError.set(this, (error) => {
      __classPrivateFieldSet(this, _MessageStream_errored, true);
      if (isAbortError(error)) {
        error = new APIUserAbortError();
      }
      if (error instanceof APIUserAbortError) {
        __classPrivateFieldSet(this, _MessageStream_aborted, true);
        return this._emit("abort", error);
      }
      if (error instanceof AnthropicError) {
        return this._emit("error", error);
      }
      if (error instanceof Error) {
        const anthropicError = new AnthropicError(error.message);
        anthropicError.cause = error;
        return this._emit("error", anthropicError);
      }
      return this._emit("error", new AnthropicError(String(error)));
    });
    __classPrivateFieldSet(this, _MessageStream_connectedPromise, new Promise((resolve, reject) => {
      __classPrivateFieldSet(this, _MessageStream_resolveConnectedPromise, resolve, "f");
      __classPrivateFieldSet(this, _MessageStream_rejectConnectedPromise, reject, "f");
    }));
    __classPrivateFieldSet(this, _MessageStream_endPromise, new Promise((resolve, reject) => {
      __classPrivateFieldSet(this, _MessageStream_resolveEndPromise, resolve, "f");
      __classPrivateFieldSet(this, _MessageStream_rejectEndPromise, reject, "f");
    }));
    __classPrivateFieldGet(this, _MessageStream_connectedPromise, "f").catch(() => {
    });
    __classPrivateFieldGet(this, _MessageStream_endPromise, "f").catch(() => {
    });
  }
  get response() {
    return __classPrivateFieldGet(this, _MessageStream_response, "f");
  }
  get request_id() {
    return __classPrivateFieldGet(this, _MessageStream_request_id, "f");
  }
  /**
   * Returns the `MessageStream` data, the raw `Response` instance and the ID of the request,
   * returned vie the `request-id` header which is useful for debugging requests and resporting
   * issues to Anthropic.
   *
   * This is the same as the `APIPromise.withResponse()` method.
   *
   * This method will raise an error if you created the stream using `MessageStream.fromReadableStream`
   * as no `Response` is available.
   */
  async withResponse() {
    const response = await __classPrivateFieldGet(this, _MessageStream_connectedPromise, "f");
    if (!response) {
      throw new Error("Could not resolve a `Response` object");
    }
    return {
      data: this,
      response,
      request_id: response.headers.get("request-id")
    };
  }
  /**
   * Intended for use on the frontend, consuming a stream produced with
   * `.toReadableStream()` on the backend.
   *
   * Note that messages sent to the model do not appear in `.on('message')`
   * in this context.
   */
  static fromReadableStream(stream) {
    const runner = new MessageStream();
    runner._run(() => runner._fromReadableStream(stream));
    return runner;
  }
  static createMessage(messages, params, options) {
    const runner = new MessageStream();
    for (const message of params.messages) {
      runner._addMessageParam(message);
    }
    runner._run(() => runner._createMessage(messages, { ...params, stream: true }, { ...options, headers: { ...options?.headers, "X-Stainless-Helper-Method": "stream" } }));
    return runner;
  }
  _run(executor) {
    executor().then(() => {
      this._emitFinal();
      this._emit("end");
    }, __classPrivateFieldGet(this, _MessageStream_handleError, "f"));
  }
  _addMessageParam(message) {
    this.messages.push(message);
  }
  _addMessage(message, emit = true) {
    this.receivedMessages.push(message);
    if (emit) {
      this._emit("message", message);
    }
  }
  async _createMessage(messages, params, options) {
    const signal = options?.signal;
    let abortHandler;
    if (signal) {
      if (signal.aborted)
        this.controller.abort();
      abortHandler = this.controller.abort.bind(this.controller);
      signal.addEventListener("abort", abortHandler);
    }
    try {
      __classPrivateFieldGet(this, _MessageStream_instances, "m", _MessageStream_beginRequest).call(this);
      const { response, data: stream } = await messages.create({ ...params, stream: true }, { ...options, signal: this.controller.signal }).withResponse();
      this._connected(response);
      for await (const event of stream) {
        __classPrivateFieldGet(this, _MessageStream_instances, "m", _MessageStream_addStreamEvent).call(this, event);
      }
      if (stream.controller.signal?.aborted) {
        throw new APIUserAbortError();
      }
      __classPrivateFieldGet(this, _MessageStream_instances, "m", _MessageStream_endRequest).call(this);
    } finally {
      if (signal && abortHandler) {
        signal.removeEventListener("abort", abortHandler);
      }
    }
  }
  _connected(response) {
    if (this.ended)
      return;
    __classPrivateFieldSet(this, _MessageStream_response, response);
    __classPrivateFieldSet(this, _MessageStream_request_id, response?.headers.get("request-id"));
    __classPrivateFieldGet(this, _MessageStream_resolveConnectedPromise, "f").call(this, response);
    this._emit("connect");
  }
  get ended() {
    return __classPrivateFieldGet(this, _MessageStream_ended, "f");
  }
  get errored() {
    return __classPrivateFieldGet(this, _MessageStream_errored, "f");
  }
  get aborted() {
    return __classPrivateFieldGet(this, _MessageStream_aborted, "f");
  }
  abort() {
    this.controller.abort();
  }
  /**
   * Adds the listener function to the end of the listeners array for the event.
   * No checks are made to see if the listener has already been added. Multiple calls passing
   * the same combination of event and listener will result in the listener being added, and
   * called, multiple times.
   * @returns this MessageStream, so that calls can be chained
   */
  on(event, listener) {
    const listeners = __classPrivateFieldGet(this, _MessageStream_listeners, "f")[event] || (__classPrivateFieldGet(this, _MessageStream_listeners, "f")[event] = []);
    listeners.push({ listener });
    return this;
  }
  /**
   * Removes the specified listener from the listener array for the event.
   * off() will remove, at most, one instance of a listener from the listener array. If any single
   * listener has been added multiple times to the listener array for the specified event, then
   * off() must be called multiple times to remove each instance.
   * @returns this MessageStream, so that calls can be chained
   */
  off(event, listener) {
    const listeners = __classPrivateFieldGet(this, _MessageStream_listeners, "f")[event];
    if (!listeners)
      return this;
    const index = listeners.findIndex((l) => l.listener === listener);
    if (index >= 0)
      listeners.splice(index, 1);
    return this;
  }
  /**
   * Adds a one-time listener function for the event. The next time the event is triggered,
   * this listener is removed and then invoked.
   * @returns this MessageStream, so that calls can be chained
   */
  once(event, listener) {
    const listeners = __classPrivateFieldGet(this, _MessageStream_listeners, "f")[event] || (__classPrivateFieldGet(this, _MessageStream_listeners, "f")[event] = []);
    listeners.push({ listener, once: true });
    return this;
  }
  /**
   * This is similar to `.once()`, but returns a Promise that resolves the next time
   * the event is triggered, instead of calling a listener callback.
   * @returns a Promise that resolves the next time given event is triggered,
   * or rejects if an error is emitted.  (If you request the 'error' event,
   * returns a promise that resolves with the error).
   *
   * Example:
   *
   *   const message = await stream.emitted('message') // rejects if the stream errors
   */
  emitted(event) {
    return new Promise((resolve, reject) => {
      __classPrivateFieldSet(this, _MessageStream_catchingPromiseCreated, true);
      if (event !== "error")
        this.once("error", reject);
      this.once(event, resolve);
    });
  }
  async done() {
    __classPrivateFieldSet(this, _MessageStream_catchingPromiseCreated, true);
    await __classPrivateFieldGet(this, _MessageStream_endPromise, "f");
  }
  get currentMessage() {
    return __classPrivateFieldGet(this, _MessageStream_currentMessageSnapshot, "f");
  }
  /**
   * @returns a promise that resolves with the the final assistant Message response,
   * or rejects if an error occurred or the stream ended prematurely without producing a Message.
   */
  async finalMessage() {
    await this.done();
    return __classPrivateFieldGet(this, _MessageStream_instances, "m", _MessageStream_getFinalMessage).call(this);
  }
  /**
   * @returns a promise that resolves with the the final assistant Message's text response, concatenated
   * together if there are more than one text blocks.
   * Rejects if an error occurred or the stream ended prematurely without producing a Message.
   */
  async finalText() {
    await this.done();
    return __classPrivateFieldGet(this, _MessageStream_instances, "m", _MessageStream_getFinalText).call(this);
  }
  _emit(event, ...args) {
    if (__classPrivateFieldGet(this, _MessageStream_ended, "f"))
      return;
    if (event === "end") {
      __classPrivateFieldSet(this, _MessageStream_ended, true);
      __classPrivateFieldGet(this, _MessageStream_resolveEndPromise, "f").call(this);
    }
    const listeners = __classPrivateFieldGet(this, _MessageStream_listeners, "f")[event];
    if (listeners) {
      __classPrivateFieldGet(this, _MessageStream_listeners, "f")[event] = listeners.filter((l) => !l.once);
      listeners.forEach(({ listener }) => listener(...args));
    }
    if (event === "abort") {
      const error = args[0];
      if (!__classPrivateFieldGet(this, _MessageStream_catchingPromiseCreated, "f") && !listeners?.length) {
        Promise.reject(error);
      }
      __classPrivateFieldGet(this, _MessageStream_rejectConnectedPromise, "f").call(this, error);
      __classPrivateFieldGet(this, _MessageStream_rejectEndPromise, "f").call(this, error);
      this._emit("end");
      return;
    }
    if (event === "error") {
      const error = args[0];
      if (!__classPrivateFieldGet(this, _MessageStream_catchingPromiseCreated, "f") && !listeners?.length) {
        Promise.reject(error);
      }
      __classPrivateFieldGet(this, _MessageStream_rejectConnectedPromise, "f").call(this, error);
      __classPrivateFieldGet(this, _MessageStream_rejectEndPromise, "f").call(this, error);
      this._emit("end");
    }
  }
  _emitFinal() {
    const finalMessage = this.receivedMessages.at(-1);
    if (finalMessage) {
      this._emit("finalMessage", __classPrivateFieldGet(this, _MessageStream_instances, "m", _MessageStream_getFinalMessage).call(this));
    }
  }
  async _fromReadableStream(readableStream, options) {
    const signal = options?.signal;
    let abortHandler;
    if (signal) {
      if (signal.aborted)
        this.controller.abort();
      abortHandler = this.controller.abort.bind(this.controller);
      signal.addEventListener("abort", abortHandler);
    }
    try {
      __classPrivateFieldGet(this, _MessageStream_instances, "m", _MessageStream_beginRequest).call(this);
      this._connected(null);
      const stream = Stream.fromReadableStream(readableStream, this.controller);
      for await (const event of stream) {
        __classPrivateFieldGet(this, _MessageStream_instances, "m", _MessageStream_addStreamEvent).call(this, event);
      }
      if (stream.controller.signal?.aborted) {
        throw new APIUserAbortError();
      }
      __classPrivateFieldGet(this, _MessageStream_instances, "m", _MessageStream_endRequest).call(this);
    } finally {
      if (signal && abortHandler) {
        signal.removeEventListener("abort", abortHandler);
      }
    }
  }
  [(_MessageStream_currentMessageSnapshot = /* @__PURE__ */ new WeakMap(), _MessageStream_connectedPromise = /* @__PURE__ */ new WeakMap(), _MessageStream_resolveConnectedPromise = /* @__PURE__ */ new WeakMap(), _MessageStream_rejectConnectedPromise = /* @__PURE__ */ new WeakMap(), _MessageStream_endPromise = /* @__PURE__ */ new WeakMap(), _MessageStream_resolveEndPromise = /* @__PURE__ */ new WeakMap(), _MessageStream_rejectEndPromise = /* @__PURE__ */ new WeakMap(), _MessageStream_listeners = /* @__PURE__ */ new WeakMap(), _MessageStream_ended = /* @__PURE__ */ new WeakMap(), _MessageStream_errored = /* @__PURE__ */ new WeakMap(), _MessageStream_aborted = /* @__PURE__ */ new WeakMap(), _MessageStream_catchingPromiseCreated = /* @__PURE__ */ new WeakMap(), _MessageStream_response = /* @__PURE__ */ new WeakMap(), _MessageStream_request_id = /* @__PURE__ */ new WeakMap(), _MessageStream_handleError = /* @__PURE__ */ new WeakMap(), _MessageStream_instances = /* @__PURE__ */ new WeakSet(), _MessageStream_getFinalMessage = function _MessageStream_getFinalMessage2() {
    if (this.receivedMessages.length === 0) {
      throw new AnthropicError("stream ended without producing a Message with role=assistant");
    }
    return this.receivedMessages.at(-1);
  }, _MessageStream_getFinalText = function _MessageStream_getFinalText2() {
    if (this.receivedMessages.length === 0) {
      throw new AnthropicError("stream ended without producing a Message with role=assistant");
    }
    const textBlocks = this.receivedMessages.at(-1).content.filter((block) => block.type === "text").map((block) => block.text);
    if (textBlocks.length === 0) {
      throw new AnthropicError("stream ended without producing a content block with type=text");
    }
    return textBlocks.join(" ");
  }, _MessageStream_beginRequest = function _MessageStream_beginRequest2() {
    if (this.ended)
      return;
    __classPrivateFieldSet(this, _MessageStream_currentMessageSnapshot, void 0);
  }, _MessageStream_addStreamEvent = function _MessageStream_addStreamEvent2(event) {
    if (this.ended)
      return;
    const messageSnapshot = __classPrivateFieldGet(this, _MessageStream_instances, "m", _MessageStream_accumulateMessage).call(this, event);
    this._emit("streamEvent", event, messageSnapshot);
    switch (event.type) {
      case "content_block_delta": {
        const content = messageSnapshot.content.at(-1);
        switch (event.delta.type) {
          case "text_delta": {
            if (content.type === "text") {
              this._emit("text", event.delta.text, content.text || "");
            }
            break;
          }
          case "citations_delta": {
            if (content.type === "text") {
              this._emit("citation", event.delta.citation, content.citations ?? []);
            }
            break;
          }
          case "input_json_delta": {
            if (tracksToolInput(content) && content.input) {
              this._emit("inputJson", event.delta.partial_json, content.input);
            }
            break;
          }
          case "thinking_delta": {
            if (content.type === "thinking") {
              this._emit("thinking", event.delta.thinking, content.thinking);
            }
            break;
          }
          case "signature_delta": {
            if (content.type === "thinking") {
              this._emit("signature", content.signature);
            }
            break;
          }
          default:
            checkNever(event.delta);
        }
        break;
      }
      case "message_stop": {
        this._addMessageParam(messageSnapshot);
        this._addMessage(messageSnapshot, true);
        break;
      }
      case "content_block_stop": {
        this._emit("contentBlock", messageSnapshot.content.at(-1));
        break;
      }
      case "message_start": {
        __classPrivateFieldSet(this, _MessageStream_currentMessageSnapshot, messageSnapshot);
        break;
      }
    }
  }, _MessageStream_endRequest = function _MessageStream_endRequest2() {
    if (this.ended) {
      throw new AnthropicError(`stream has ended, this shouldn't happen`);
    }
    const snapshot = __classPrivateFieldGet(this, _MessageStream_currentMessageSnapshot, "f");
    if (!snapshot) {
      throw new AnthropicError(`request ended without sending any chunks`);
    }
    __classPrivateFieldSet(this, _MessageStream_currentMessageSnapshot, void 0);
    return snapshot;
  }, _MessageStream_accumulateMessage = function _MessageStream_accumulateMessage2(event) {
    let snapshot = __classPrivateFieldGet(this, _MessageStream_currentMessageSnapshot, "f");
    if (event.type === "message_start") {
      if (snapshot) {
        throw new AnthropicError(`Unexpected event order, got ${event.type} before receiving "message_stop"`);
      }
      return event.message;
    }
    if (!snapshot) {
      throw new AnthropicError(`Unexpected event order, got ${event.type} before "message_start"`);
    }
    switch (event.type) {
      case "message_stop":
        return snapshot;
      case "message_delta":
        snapshot.stop_reason = event.delta.stop_reason;
        snapshot.stop_sequence = event.delta.stop_sequence;
        snapshot.usage.output_tokens = event.usage.output_tokens;
        if (event.usage.input_tokens != null) {
          snapshot.usage.input_tokens = event.usage.input_tokens;
        }
        if (event.usage.cache_creation_input_tokens != null) {
          snapshot.usage.cache_creation_input_tokens = event.usage.cache_creation_input_tokens;
        }
        if (event.usage.cache_read_input_tokens != null) {
          snapshot.usage.cache_read_input_tokens = event.usage.cache_read_input_tokens;
        }
        if (event.usage.server_tool_use != null) {
          snapshot.usage.server_tool_use = event.usage.server_tool_use;
        }
        return snapshot;
      case "content_block_start":
        snapshot.content.push({ ...event.content_block });
        return snapshot;
      case "content_block_delta": {
        const snapshotContent = snapshot.content.at(event.index);
        switch (event.delta.type) {
          case "text_delta": {
            if (snapshotContent?.type === "text") {
              snapshot.content[event.index] = {
                ...snapshotContent,
                text: (snapshotContent.text || "") + event.delta.text
              };
            }
            break;
          }
          case "citations_delta": {
            if (snapshotContent?.type === "text") {
              snapshot.content[event.index] = {
                ...snapshotContent,
                citations: [...snapshotContent.citations ?? [], event.delta.citation]
              };
            }
            break;
          }
          case "input_json_delta": {
            if (snapshotContent && tracksToolInput(snapshotContent)) {
              let jsonBuf = snapshotContent[JSON_BUF_PROPERTY] || "";
              jsonBuf += event.delta.partial_json;
              const newContent = { ...snapshotContent };
              Object.defineProperty(newContent, JSON_BUF_PROPERTY, {
                value: jsonBuf,
                enumerable: false,
                writable: true
              });
              if (jsonBuf) {
                newContent.input = partialParse(jsonBuf);
              }
              snapshot.content[event.index] = newContent;
            }
            break;
          }
          case "thinking_delta": {
            if (snapshotContent?.type === "thinking") {
              snapshot.content[event.index] = {
                ...snapshotContent,
                thinking: snapshotContent.thinking + event.delta.thinking
              };
            }
            break;
          }
          case "signature_delta": {
            if (snapshotContent?.type === "thinking") {
              snapshot.content[event.index] = {
                ...snapshotContent,
                signature: event.delta.signature
              };
            }
            break;
          }
          default:
            checkNever(event.delta);
        }
        return snapshot;
      }
      case "content_block_stop":
        return snapshot;
    }
  }, Symbol.asyncIterator)]() {
    const pushQueue = [];
    const readQueue = [];
    let done = false;
    this.on("streamEvent", (event) => {
      const reader = readQueue.shift();
      if (reader) {
        reader.resolve(event);
      } else {
        pushQueue.push(event);
      }
    });
    this.on("end", () => {
      done = true;
      for (const reader of readQueue) {
        reader.resolve(void 0);
      }
      readQueue.length = 0;
    });
    this.on("abort", (err) => {
      done = true;
      for (const reader of readQueue) {
        reader.reject(err);
      }
      readQueue.length = 0;
    });
    this.on("error", (err) => {
      done = true;
      for (const reader of readQueue) {
        reader.reject(err);
      }
      readQueue.length = 0;
    });
    return {
      next: async () => {
        if (!pushQueue.length) {
          if (done) {
            return { value: void 0, done: true };
          }
          return new Promise((resolve, reject) => readQueue.push({ resolve, reject })).then((chunk2) => chunk2 ? { value: chunk2, done: false } : { value: void 0, done: true });
        }
        const chunk = pushQueue.shift();
        return { value: chunk, done: false };
      },
      return: async () => {
        this.abort();
        return { value: void 0, done: true };
      }
    };
  }
  toReadableStream() {
    const stream = new Stream(this[Symbol.asyncIterator].bind(this), this.controller);
    return stream.toReadableStream();
  }
}
function checkNever(x) {
}
class Batches2 extends APIResource {
  /**
   * Send a batch of Message creation requests.
   *
   * The Message Batches API can be used to process multiple Messages API requests at
   * once. Once a Message Batch is created, it begins processing immediately. Batches
   * can take up to 24 hours to complete.
   *
   * Learn more about the Message Batches API in our
   * [user guide](/en/docs/build-with-claude/batch-processing)
   *
   * @example
   * ```ts
   * const messageBatch = await client.messages.batches.create({
   *   requests: [
   *     {
   *       custom_id: 'my-custom-id-1',
   *       params: {
   *         max_tokens: 1024,
   *         messages: [
   *           { content: 'Hello, world', role: 'user' },
   *         ],
   *         model: 'claude-sonnet-4-20250514',
   *       },
   *     },
   *   ],
   * });
   * ```
   */
  create(body, options) {
    return this._client.post("/v1/messages/batches", { body, ...options });
  }
  /**
   * This endpoint is idempotent and can be used to poll for Message Batch
   * completion. To access the results of a Message Batch, make a request to the
   * `results_url` field in the response.
   *
   * Learn more about the Message Batches API in our
   * [user guide](/en/docs/build-with-claude/batch-processing)
   *
   * @example
   * ```ts
   * const messageBatch = await client.messages.batches.retrieve(
   *   'message_batch_id',
   * );
   * ```
   */
  retrieve(messageBatchID, options) {
    return this._client.get(path`/v1/messages/batches/${messageBatchID}`, options);
  }
  /**
   * List all Message Batches within a Workspace. Most recently created batches are
   * returned first.
   *
   * Learn more about the Message Batches API in our
   * [user guide](/en/docs/build-with-claude/batch-processing)
   *
   * @example
   * ```ts
   * // Automatically fetches more pages as needed.
   * for await (const messageBatch of client.messages.batches.list()) {
   *   // ...
   * }
   * ```
   */
  list(query = {}, options) {
    return this._client.getAPIList("/v1/messages/batches", Page, { query, ...options });
  }
  /**
   * Delete a Message Batch.
   *
   * Message Batches can only be deleted once they've finished processing. If you'd
   * like to delete an in-progress batch, you must first cancel it.
   *
   * Learn more about the Message Batches API in our
   * [user guide](/en/docs/build-with-claude/batch-processing)
   *
   * @example
   * ```ts
   * const deletedMessageBatch =
   *   await client.messages.batches.delete('message_batch_id');
   * ```
   */
  delete(messageBatchID, options) {
    return this._client.delete(path`/v1/messages/batches/${messageBatchID}`, options);
  }
  /**
   * Batches may be canceled any time before processing ends. Once cancellation is
   * initiated, the batch enters a `canceling` state, at which time the system may
   * complete any in-progress, non-interruptible requests before finalizing
   * cancellation.
   *
   * The number of canceled requests is specified in `request_counts`. To determine
   * which requests were canceled, check the individual results within the batch.
   * Note that cancellation may not result in any canceled requests if they were
   * non-interruptible.
   *
   * Learn more about the Message Batches API in our
   * [user guide](/en/docs/build-with-claude/batch-processing)
   *
   * @example
   * ```ts
   * const messageBatch = await client.messages.batches.cancel(
   *   'message_batch_id',
   * );
   * ```
   */
  cancel(messageBatchID, options) {
    return this._client.post(path`/v1/messages/batches/${messageBatchID}/cancel`, options);
  }
  /**
   * Streams the results of a Message Batch as a `.jsonl` file.
   *
   * Each line in the file is a JSON object containing the result of a single request
   * in the Message Batch. Results are not guaranteed to be in the same order as
   * requests. Use the `custom_id` field to match results to requests.
   *
   * Learn more about the Message Batches API in our
   * [user guide](/en/docs/build-with-claude/batch-processing)
   *
   * @example
   * ```ts
   * const messageBatchIndividualResponse =
   *   await client.messages.batches.results('message_batch_id');
   * ```
   */
  async results(messageBatchID, options) {
    const batch = await this.retrieve(messageBatchID);
    if (!batch.results_url) {
      throw new AnthropicError(`No batch \`results_url\`; Has it finished processing? ${batch.processing_status} - ${batch.id}`);
    }
    return this._client.get(batch.results_url, {
      ...options,
      headers: buildHeaders([{ Accept: "application/binary" }, options?.headers]),
      stream: true,
      __binaryResponse: true
    })._thenUnwrap((_, props) => JSONLDecoder.fromResponse(props.response, props.controller));
  }
}
class Messages2 extends APIResource {
  constructor() {
    super(...arguments);
    this.batches = new Batches2(this._client);
  }
  create(body, options) {
    if (body.model in DEPRECATED_MODELS) {
      console.warn(`The model '${body.model}' is deprecated and will reach end-of-life on ${DEPRECATED_MODELS[body.model]}
Please migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);
    }
    let timeout = this._client._options.timeout;
    if (!body.stream && timeout == null) {
      const maxNonstreamingTokens = MODEL_NONSTREAMING_TOKENS[body.model] ?? void 0;
      timeout = this._client.calculateNonstreamingTimeout(body.max_tokens, maxNonstreamingTokens);
    }
    return this._client.post("/v1/messages", {
      body,
      timeout: timeout ?? 6e5,
      ...options,
      stream: body.stream ?? false
    });
  }
  /**
   * Create a Message stream
   */
  stream(body, options) {
    return MessageStream.createMessage(this, body, options);
  }
  /**
   * Count the number of tokens in a Message.
   *
   * The Token Count API can be used to count the number of tokens in a Message,
   * including tools, images, and documents, without creating it.
   *
   * Learn more about token counting in our
   * [user guide](/en/docs/build-with-claude/token-counting)
   *
   * @example
   * ```ts
   * const messageTokensCount =
   *   await client.messages.countTokens({
   *     messages: [{ content: 'string', role: 'user' }],
   *     model: 'claude-3-7-sonnet-latest',
   *   });
   * ```
   */
  countTokens(body, options) {
    return this._client.post("/v1/messages/count_tokens", { body, ...options });
  }
}
const DEPRECATED_MODELS = {
  "claude-1.3": "November 6th, 2024",
  "claude-1.3-100k": "November 6th, 2024",
  "claude-instant-1.1": "November 6th, 2024",
  "claude-instant-1.1-100k": "November 6th, 2024",
  "claude-instant-1.2": "November 6th, 2024",
  "claude-3-sonnet-20240229": "July 21st, 2025",
  "claude-3-opus-20240229": "January 5th, 2026",
  "claude-2.1": "July 21st, 2025",
  "claude-2.0": "July 21st, 2025",
  "claude-3-5-sonnet-20241022": "October 22, 2025",
  "claude-3-5-sonnet-20240620": "October 22, 2025"
};
Messages2.Batches = Batches2;
class Models2 extends APIResource {
  /**
   * Get a specific model.
   *
   * The Models API response can be used to determine information about a specific
   * model or resolve a model alias to a model ID.
   */
  retrieve(modelID, params = {}, options) {
    const { betas } = params ?? {};
    return this._client.get(path`/v1/models/${modelID}`, {
      ...options,
      headers: buildHeaders([
        { ...betas?.toString() != null ? { "anthropic-beta": betas?.toString() } : void 0 },
        options?.headers
      ])
    });
  }
  /**
   * List available models.
   *
   * The Models API response can be used to determine which models are available for
   * use in the API. More recently released models are listed first.
   */
  list(params = {}, options) {
    const { betas, ...query } = params ?? {};
    return this._client.getAPIList("/v1/models", Page, {
      query,
      ...options,
      headers: buildHeaders([
        { ...betas?.toString() != null ? { "anthropic-beta": betas?.toString() } : void 0 },
        options?.headers
      ])
    });
  }
}
const readEnv = (env) => {
  if (typeof globalThis.process !== "undefined") {
    return globalThis.process.env?.[env]?.trim() ?? void 0;
  }
  if (typeof globalThis.Deno !== "undefined") {
    return globalThis.Deno.env?.get?.(env)?.trim();
  }
  return void 0;
};
var _BaseAnthropic_instances, _a, _BaseAnthropic_encoder, _BaseAnthropic_baseURLOverridden;
const HUMAN_PROMPT = "\\n\\nHuman:";
const AI_PROMPT = "\\n\\nAssistant:";
class BaseAnthropic {
  /**
   * API Client for interfacing with the Anthropic API.
   *
   * @param {string | null | undefined} [opts.apiKey=process.env['ANTHROPIC_API_KEY'] ?? null]
   * @param {string | null | undefined} [opts.authToken=process.env['ANTHROPIC_AUTH_TOKEN'] ?? null]
   * @param {string} [opts.baseURL=process.env['ANTHROPIC_BASE_URL'] ?? https://api.anthropic.com] - Override the default base URL for the API.
   * @param {number} [opts.timeout=10 minutes] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.
   * @param {MergedRequestInit} [opts.fetchOptions] - Additional `RequestInit` options to be passed to `fetch` calls.
   * @param {Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.
   * @param {number} [opts.maxRetries=2] - The maximum number of times the client will retry a request.
   * @param {HeadersLike} opts.defaultHeaders - Default headers to include with every request to the API.
   * @param {Record<string, string | undefined>} opts.defaultQuery - Default query parameters to include with every request to the API.
   * @param {boolean} [opts.dangerouslyAllowBrowser=false] - By default, client-side use of this library is not allowed, as it risks exposing your secret API credentials to attackers.
   */
  constructor({ baseURL = readEnv("ANTHROPIC_BASE_URL"), apiKey = readEnv("ANTHROPIC_API_KEY") ?? null, authToken = readEnv("ANTHROPIC_AUTH_TOKEN") ?? null, ...opts } = {}) {
    _BaseAnthropic_instances.add(this);
    _BaseAnthropic_encoder.set(this, void 0);
    const options = {
      apiKey,
      authToken,
      ...opts,
      baseURL: baseURL || `https://api.anthropic.com`
    };
    if (!options.dangerouslyAllowBrowser && isRunningInBrowser()) {
      throw new AnthropicError("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew Anthropic({ apiKey, dangerouslyAllowBrowser: true });\n");
    }
    this.baseURL = options.baseURL;
    this.timeout = options.timeout ?? _a.DEFAULT_TIMEOUT;
    this.logger = options.logger ?? console;
    const defaultLogLevel = "warn";
    this.logLevel = defaultLogLevel;
    this.logLevel = parseLogLevel(options.logLevel, "ClientOptions.logLevel", this) ?? parseLogLevel(readEnv("ANTHROPIC_LOG"), "process.env['ANTHROPIC_LOG']", this) ?? defaultLogLevel;
    this.fetchOptions = options.fetchOptions;
    this.maxRetries = options.maxRetries ?? 2;
    this.fetch = options.fetch ?? getDefaultFetch();
    __classPrivateFieldSet(this, _BaseAnthropic_encoder, FallbackEncoder);
    this._options = options;
    this.apiKey = apiKey;
    this.authToken = authToken;
  }
  /**
   * Create a new client instance re-using the same options given to the current client with optional overriding.
   */
  withOptions(options) {
    const client = new this.constructor({
      ...this._options,
      baseURL: this.baseURL,
      maxRetries: this.maxRetries,
      timeout: this.timeout,
      logger: this.logger,
      logLevel: this.logLevel,
      fetch: this.fetch,
      fetchOptions: this.fetchOptions,
      apiKey: this.apiKey,
      authToken: this.authToken,
      ...options
    });
    return client;
  }
  defaultQuery() {
    return this._options.defaultQuery;
  }
  validateHeaders({ values, nulls }) {
    if (this.apiKey && values.get("x-api-key")) {
      return;
    }
    if (nulls.has("x-api-key")) {
      return;
    }
    if (this.authToken && values.get("authorization")) {
      return;
    }
    if (nulls.has("authorization")) {
      return;
    }
    throw new Error('Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the "X-Api-Key" or "Authorization" headers to be explicitly omitted');
  }
  async authHeaders(opts) {
    return buildHeaders([await this.apiKeyAuth(opts), await this.bearerAuth(opts)]);
  }
  async apiKeyAuth(opts) {
    if (this.apiKey == null) {
      return void 0;
    }
    return buildHeaders([{ "X-Api-Key": this.apiKey }]);
  }
  async bearerAuth(opts) {
    if (this.authToken == null) {
      return void 0;
    }
    return buildHeaders([{ Authorization: `Bearer ${this.authToken}` }]);
  }
  /**
   * Basic re-implementation of `qs.stringify` for primitive types.
   */
  stringifyQuery(query) {
    return Object.entries(query).filter(([_, value]) => typeof value !== "undefined").map(([key, value]) => {
      if (typeof value === "string" || typeof value === "number" || typeof value === "boolean") {
        return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
      }
      if (value === null) {
        return `${encodeURIComponent(key)}=`;
      }
      throw new AnthropicError(`Cannot stringify type ${typeof value}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`);
    }).join("&");
  }
  getUserAgent() {
    return `${this.constructor.name}/JS ${VERSION}`;
  }
  defaultIdempotencyKey() {
    return `stainless-node-retry-${uuid4()}`;
  }
  makeStatusError(status, error, message, headers) {
    return APIError.generate(status, error, message, headers);
  }
  buildURL(path2, query, defaultBaseURL) {
    const baseURL = !__classPrivateFieldGet(this, _BaseAnthropic_instances, "m", _BaseAnthropic_baseURLOverridden).call(this) && defaultBaseURL || this.baseURL;
    const url = isAbsoluteURL(path2) ? new URL(path2) : new URL(baseURL + (baseURL.endsWith("/") && path2.startsWith("/") ? path2.slice(1) : path2));
    const defaultQuery = this.defaultQuery();
    if (!isEmptyObj(defaultQuery)) {
      query = { ...defaultQuery, ...query };
    }
    if (typeof query === "object" && query && !Array.isArray(query)) {
      url.search = this.stringifyQuery(query);
    }
    return url.toString();
  }
  _calculateNonstreamingTimeout(maxTokens) {
    const defaultTimeout = 10 * 60;
    const expectedTimeout = 60 * 60 * maxTokens / 128e3;
    if (expectedTimeout > defaultTimeout) {
      throw new AnthropicError("Streaming is required for operations that may take longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-typescript#streaming-responses for more details");
    }
    return defaultTimeout * 1e3;
  }
  /**
   * Used as a callback for mutating the given `FinalRequestOptions` object.
   */
  async prepareOptions(options) {
  }
  /**
   * Used as a callback for mutating the given `RequestInit` object.
   *
   * This is useful for cases where you want to add certain headers based off of
   * the request properties, e.g. `method` or `url`.
   */
  async prepareRequest(request, { url, options }) {
  }
  get(path2, opts) {
    return this.methodRequest("get", path2, opts);
  }
  post(path2, opts) {
    return this.methodRequest("post", path2, opts);
  }
  patch(path2, opts) {
    return this.methodRequest("patch", path2, opts);
  }
  put(path2, opts) {
    return this.methodRequest("put", path2, opts);
  }
  delete(path2, opts) {
    return this.methodRequest("delete", path2, opts);
  }
  methodRequest(method, path2, opts) {
    return this.request(Promise.resolve(opts).then((opts2) => {
      return { method, path: path2, ...opts2 };
    }));
  }
  request(options, remainingRetries = null) {
    return new APIPromise(this, this.makeRequest(options, remainingRetries, void 0));
  }
  async makeRequest(optionsInput, retriesRemaining, retryOfRequestLogID) {
    const options = await optionsInput;
    const maxRetries = options.maxRetries ?? this.maxRetries;
    if (retriesRemaining == null) {
      retriesRemaining = maxRetries;
    }
    await this.prepareOptions(options);
    const { req, url, timeout } = await this.buildRequest(options, {
      retryCount: maxRetries - retriesRemaining
    });
    await this.prepareRequest(req, { url, options });
    const requestLogID = "log_" + (Math.random() * (1 << 24) | 0).toString(16).padStart(6, "0");
    const retryLogStr = retryOfRequestLogID === void 0 ? "" : `, retryOf: ${retryOfRequestLogID}`;
    const startTime = Date.now();
    loggerFor(this).debug(`[${requestLogID}] sending request`, formatRequestDetails({
      retryOfRequestLogID,
      method: options.method,
      url,
      options,
      headers: req.headers
    }));
    if (options.signal?.aborted) {
      throw new APIUserAbortError();
    }
    const controller = new AbortController();
    const response = await this.fetchWithTimeout(url, req, timeout, controller).catch(castToError);
    const headersTime = Date.now();
    if (response instanceof globalThis.Error) {
      const retryMessage = `retrying, ${retriesRemaining} attempts remaining`;
      if (options.signal?.aborted) {
        throw new APIUserAbortError();
      }
      const isTimeout = isAbortError(response) || /timed? ?out/i.test(String(response) + ("cause" in response ? String(response.cause) : ""));
      if (retriesRemaining) {
        loggerFor(this).info(`[${requestLogID}] connection ${isTimeout ? "timed out" : "failed"} - ${retryMessage}`);
        loggerFor(this).debug(`[${requestLogID}] connection ${isTimeout ? "timed out" : "failed"} (${retryMessage})`, formatRequestDetails({
          retryOfRequestLogID,
          url,
          durationMs: headersTime - startTime,
          message: response.message
        }));
        return this.retryRequest(options, retriesRemaining, retryOfRequestLogID ?? requestLogID);
      }
      loggerFor(this).info(`[${requestLogID}] connection ${isTimeout ? "timed out" : "failed"} - error; no more retries left`);
      loggerFor(this).debug(`[${requestLogID}] connection ${isTimeout ? "timed out" : "failed"} (error; no more retries left)`, formatRequestDetails({
        retryOfRequestLogID,
        url,
        durationMs: headersTime - startTime,
        message: response.message
      }));
      if (isTimeout) {
        throw new APIConnectionTimeoutError();
      }
      throw new APIConnectionError({ cause: response });
    }
    const specialHeaders = [...response.headers.entries()].filter(([name]) => name === "request-id").map(([name, value]) => ", " + name + ": " + JSON.stringify(value)).join("");
    const responseInfo = `[${requestLogID}${retryLogStr}${specialHeaders}] ${req.method} ${url} ${response.ok ? "succeeded" : "failed"} with status ${response.status} in ${headersTime - startTime}ms`;
    if (!response.ok) {
      const shouldRetry = await this.shouldRetry(response);
      if (retriesRemaining && shouldRetry) {
        const retryMessage2 = `retrying, ${retriesRemaining} attempts remaining`;
        await CancelReadableStream(response.body);
        loggerFor(this).info(`${responseInfo} - ${retryMessage2}`);
        loggerFor(this).debug(`[${requestLogID}] response error (${retryMessage2})`, formatRequestDetails({
          retryOfRequestLogID,
          url: response.url,
          status: response.status,
          headers: response.headers,
          durationMs: headersTime - startTime
        }));
        return this.retryRequest(options, retriesRemaining, retryOfRequestLogID ?? requestLogID, response.headers);
      }
      const retryMessage = shouldRetry ? `error; no more retries left` : `error; not retryable`;
      loggerFor(this).info(`${responseInfo} - ${retryMessage}`);
      const errText = await response.text().catch((err2) => castToError(err2).message);
      const errJSON = safeJSON(errText);
      const errMessage = errJSON ? void 0 : errText;
      loggerFor(this).debug(`[${requestLogID}] response error (${retryMessage})`, formatRequestDetails({
        retryOfRequestLogID,
        url: response.url,
        status: response.status,
        headers: response.headers,
        message: errMessage,
        durationMs: Date.now() - startTime
      }));
      const err = this.makeStatusError(response.status, errJSON, errMessage, response.headers);
      throw err;
    }
    loggerFor(this).info(responseInfo);
    loggerFor(this).debug(`[${requestLogID}] response start`, formatRequestDetails({
      retryOfRequestLogID,
      url: response.url,
      status: response.status,
      headers: response.headers,
      durationMs: headersTime - startTime
    }));
    return { response, options, controller, requestLogID, retryOfRequestLogID, startTime };
  }
  getAPIList(path2, Page2, opts) {
    return this.requestAPIList(Page2, { method: "get", path: path2, ...opts });
  }
  requestAPIList(Page2, options) {
    const request = this.makeRequest(options, null, void 0);
    return new PagePromise(this, request, Page2);
  }
  async fetchWithTimeout(url, init, ms, controller) {
    const { signal, method, ...options } = init || {};
    if (signal)
      signal.addEventListener("abort", () => controller.abort());
    const timeout = setTimeout(() => controller.abort(), ms);
    const isReadableBody = globalThis.ReadableStream && options.body instanceof globalThis.ReadableStream || typeof options.body === "object" && options.body !== null && Symbol.asyncIterator in options.body;
    const fetchOptions = {
      signal: controller.signal,
      ...isReadableBody ? { duplex: "half" } : {},
      method: "GET",
      ...options
    };
    if (method) {
      fetchOptions.method = method.toUpperCase();
    }
    try {
      return await this.fetch.call(void 0, url, fetchOptions);
    } finally {
      clearTimeout(timeout);
    }
  }
  async shouldRetry(response) {
    const shouldRetryHeader = response.headers.get("x-should-retry");
    if (shouldRetryHeader === "true")
      return true;
    if (shouldRetryHeader === "false")
      return false;
    if (response.status === 408)
      return true;
    if (response.status === 409)
      return true;
    if (response.status === 429)
      return true;
    if (response.status >= 500)
      return true;
    return false;
  }
  async retryRequest(options, retriesRemaining, requestLogID, responseHeaders) {
    let timeoutMillis;
    const retryAfterMillisHeader = responseHeaders?.get("retry-after-ms");
    if (retryAfterMillisHeader) {
      const timeoutMs = parseFloat(retryAfterMillisHeader);
      if (!Number.isNaN(timeoutMs)) {
        timeoutMillis = timeoutMs;
      }
    }
    const retryAfterHeader = responseHeaders?.get("retry-after");
    if (retryAfterHeader && !timeoutMillis) {
      const timeoutSeconds = parseFloat(retryAfterHeader);
      if (!Number.isNaN(timeoutSeconds)) {
        timeoutMillis = timeoutSeconds * 1e3;
      } else {
        timeoutMillis = Date.parse(retryAfterHeader) - Date.now();
      }
    }
    if (!(timeoutMillis && 0 <= timeoutMillis && timeoutMillis < 60 * 1e3)) {
      const maxRetries = options.maxRetries ?? this.maxRetries;
      timeoutMillis = this.calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries);
    }
    await sleep(timeoutMillis);
    return this.makeRequest(options, retriesRemaining - 1, requestLogID);
  }
  calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries) {
    const initialRetryDelay = 0.5;
    const maxRetryDelay = 8;
    const numRetries = maxRetries - retriesRemaining;
    const sleepSeconds = Math.min(initialRetryDelay * Math.pow(2, numRetries), maxRetryDelay);
    const jitter = 1 - Math.random() * 0.25;
    return sleepSeconds * jitter * 1e3;
  }
  calculateNonstreamingTimeout(maxTokens, maxNonstreamingTokens) {
    const maxTime = 60 * 60 * 1e3;
    const defaultTime = 60 * 10 * 1e3;
    const expectedTime = maxTime * maxTokens / 128e3;
    if (expectedTime > defaultTime || maxNonstreamingTokens != null && maxTokens > maxNonstreamingTokens) {
      throw new AnthropicError("Streaming is required for operations that may take longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-typescript#long-requests for more details");
    }
    return defaultTime;
  }
  async buildRequest(inputOptions, { retryCount = 0 } = {}) {
    const options = { ...inputOptions };
    const { method, path: path2, query, defaultBaseURL } = options;
    const url = this.buildURL(path2, query, defaultBaseURL);
    if ("timeout" in options)
      validatePositiveInteger("timeout", options.timeout);
    options.timeout = options.timeout ?? this.timeout;
    const { bodyHeaders, body } = this.buildBody({ options });
    const reqHeaders = await this.buildHeaders({ options: inputOptions, method, bodyHeaders, retryCount });
    const req = {
      method,
      headers: reqHeaders,
      ...options.signal && { signal: options.signal },
      ...globalThis.ReadableStream && body instanceof globalThis.ReadableStream && { duplex: "half" },
      ...body && { body },
      ...this.fetchOptions ?? {},
      ...options.fetchOptions ?? {}
    };
    return { req, url, timeout: options.timeout };
  }
  async buildHeaders({ options, method, bodyHeaders, retryCount }) {
    let idempotencyHeaders = {};
    if (this.idempotencyHeader && method !== "get") {
      if (!options.idempotencyKey)
        options.idempotencyKey = this.defaultIdempotencyKey();
      idempotencyHeaders[this.idempotencyHeader] = options.idempotencyKey;
    }
    const headers = buildHeaders([
      idempotencyHeaders,
      {
        Accept: "application/json",
        "User-Agent": this.getUserAgent(),
        "X-Stainless-Retry-Count": String(retryCount),
        ...options.timeout ? { "X-Stainless-Timeout": String(Math.trunc(options.timeout / 1e3)) } : {},
        ...getPlatformHeaders(),
        ...this._options.dangerouslyAllowBrowser ? { "anthropic-dangerous-direct-browser-access": "true" } : void 0,
        "anthropic-version": "2023-06-01"
      },
      await this.authHeaders(options),
      this._options.defaultHeaders,
      bodyHeaders,
      options.headers
    ]);
    this.validateHeaders(headers);
    return headers.values;
  }
  buildBody({ options: { body, headers: rawHeaders } }) {
    if (!body) {
      return { bodyHeaders: void 0, body: void 0 };
    }
    const headers = buildHeaders([rawHeaders]);
    if (
      // Pass raw type verbatim
      ArrayBuffer.isView(body) || body instanceof ArrayBuffer || body instanceof DataView || typeof body === "string" && // Preserve legacy string encoding behavior for now
      headers.values.has("content-type") || // `Blob` is superset of `File`
      globalThis.Blob && body instanceof globalThis.Blob || // `FormData` -> `multipart/form-data`
      body instanceof FormData || // `URLSearchParams` -> `application/x-www-form-urlencoded`
      body instanceof URLSearchParams || // Send chunked stream (each chunk has own `length`)
      globalThis.ReadableStream && body instanceof globalThis.ReadableStream
    ) {
      return { bodyHeaders: void 0, body };
    } else if (typeof body === "object" && (Symbol.asyncIterator in body || Symbol.iterator in body && "next" in body && typeof body.next === "function")) {
      return { bodyHeaders: void 0, body: ReadableStreamFrom(body) };
    } else {
      return __classPrivateFieldGet(this, _BaseAnthropic_encoder, "f").call(this, { body, headers });
    }
  }
}
_a = BaseAnthropic, _BaseAnthropic_encoder = /* @__PURE__ */ new WeakMap(), _BaseAnthropic_instances = /* @__PURE__ */ new WeakSet(), _BaseAnthropic_baseURLOverridden = function _BaseAnthropic_baseURLOverridden2() {
  return this.baseURL !== "https://api.anthropic.com";
};
BaseAnthropic.Anthropic = _a;
BaseAnthropic.HUMAN_PROMPT = HUMAN_PROMPT;
BaseAnthropic.AI_PROMPT = AI_PROMPT;
BaseAnthropic.DEFAULT_TIMEOUT = 6e5;
BaseAnthropic.AnthropicError = AnthropicError;
BaseAnthropic.APIError = APIError;
BaseAnthropic.APIConnectionError = APIConnectionError;
BaseAnthropic.APIConnectionTimeoutError = APIConnectionTimeoutError;
BaseAnthropic.APIUserAbortError = APIUserAbortError;
BaseAnthropic.NotFoundError = NotFoundError2;
BaseAnthropic.ConflictError = ConflictError;
BaseAnthropic.RateLimitError = RateLimitError;
BaseAnthropic.BadRequestError = BadRequestError;
BaseAnthropic.AuthenticationError = AuthenticationError;
BaseAnthropic.InternalServerError = InternalServerError;
BaseAnthropic.PermissionDeniedError = PermissionDeniedError;
BaseAnthropic.UnprocessableEntityError = UnprocessableEntityError;
BaseAnthropic.toFile = toFile;
class Anthropic extends BaseAnthropic {
  constructor() {
    super(...arguments);
    this.completions = new Completions(this);
    this.messages = new Messages2(this);
    this.models = new Models2(this);
    this.beta = new Beta(this);
  }
}
Anthropic.Completions = Completions;
Anthropic.Messages = Messages2;
Anthropic.Models = Models2;
Anthropic.Beta = Beta;
class ClaudeApiError extends Error {
  category;
  retryable;
  constructor(message, category, retryable = false) {
    super(message);
    this.name = "ClaudeApiError";
    this.category = category;
    this.retryable = retryable;
  }
}
class ClaudeService {
  client;
  MAX_REQUESTS_PER_MINUTE = 100;
  requestCount = 0;
  lastResetTime = Date.now();
  constructor(apiKey) {
    const key = apiKey || process.env.CLAUDE_API_KEY;
    logger.info("ClaudeService", "Initializing Claude service...");
    logger.debug("ClaudeService", `API key provided via param: ${!!apiKey}`);
    logger.debug("ClaudeService", `API key from env: ${!!process.env.CLAUDE_API_KEY}`);
    logger.debug("ClaudeService", `Final key available: ${!!key && key !== "replace"}`);
    if (!key || key === "replace") {
      logger.error("ClaudeService", "No valid API key found!");
      throw new ClaudeApiError(
        "Claude API key not configured. Please set CLAUDE_API_KEY in your environment.",
        "auth",
        false
      );
    }
    this.client = new Anthropic({
      apiKey: key,
      defaultHeaders: {
        "anthropic-beta": "files-api-2025-04-14"
      }
    });
    logger.info("ClaudeService", "Claude client initialized successfully");
  }
  resetRateLimitIfNeeded() {
    const now = Date.now();
    if (now - this.lastResetTime >= 6e4) {
      this.requestCount = 0;
      this.lastResetTime = now;
    }
  }
  async checkRateLimit() {
    this.resetRateLimitIfNeeded();
    if (this.requestCount >= this.MAX_REQUESTS_PER_MINUTE) {
      const waitTime = 6e4 - (Date.now() - this.lastResetTime);
      throw new ClaudeApiError(
        `Rate limit reached. Please wait ${Math.ceil(waitTime / 1e3)} seconds.`,
        "rate_limit",
        true
      );
    }
    this.requestCount++;
  }
  convertTextToHtml(text) {
    return text.split("\n\n").map((paragraph) => paragraph.trim()).filter((paragraph) => paragraph.length > 0).map((paragraph) => `<div>${paragraph}</div>`).join("<div><br></div>");
  }
  parseTextBlocks(responseText, leadData) {
    logger.debug("ClaudeService", "Parsing text blocks from response");
    logger.debug("ClaudeService", `Response length: ${responseText.length} characters`);
    const delimiterCount = (responseText.match(/---BLOCK---/g) || []).length;
    logger.debug("ClaudeService", `Found ${delimiterCount} occurrences of ---BLOCK--- delimiter`);
    const blocks = responseText.split("---BLOCK---").filter((block) => block.trim());
    logger.debug("ClaudeService", `Found ${blocks.length} blocks after splitting`);
    blocks.forEach((block, index) => {
      logger.debug("ClaudeService", `Block ${index + 1} (${block.length} chars): ${block.substring(0, 100)}${block.length > 100 ? "..." : ""}`);
    });
    if (blocks.length !== 7) {
      logger.error("ClaudeService", "Block count mismatch!");
      logger.error("ClaudeService", `Expected: 7 blocks, Received: ${blocks.length} blocks`);
      logger.debug("ClaudeService", `Raw response preview (first 1000 chars): ${responseText.substring(0, 1e3)}`);
      if (blocks.length === 1) {
        logger.warn("ClaudeService", "Only 1 block found. Checking if response is JSON...");
        try {
          const jsonResponse = JSON.parse(responseText);
          logger.info("ClaudeService", "Response is in JSON format. Converting to expected format...");
          if (jsonResponse.snippet1 && jsonResponse.snippet2) {
            logger.info("ClaudeService", "Found snippet fields in JSON. Using JSON response directly.");
            return {
              email: String(jsonResponse.email || leadData.email || ""),
              first_name: String(jsonResponse.first_name || leadData.first_name || ""),
              last_name: String(jsonResponse.last_name || leadData.last_name || ""),
              company: String(jsonResponse.company || leadData.company || ""),
              title: String(jsonResponse.title || leadData.title || ""),
              linkedin_url: String(jsonResponse.linkedin_url || leadData.linkedin_url || ""),
              tags: String(jsonResponse.tags || leadData.tags || ""),
              industry: String(jsonResponse.industry || leadData.industry || "Technology"),
              snippet1: String(jsonResponse.snippet1 || ""),
              snippet2: jsonResponse.snippet2.startsWith("<div>") ? jsonResponse.snippet2 : this.convertTextToHtml(jsonResponse.snippet2),
              snippet3: String(jsonResponse.snippet3 || ""),
              snippet4: jsonResponse.snippet4.startsWith("<div>") ? jsonResponse.snippet4 : this.convertTextToHtml(jsonResponse.snippet4),
              snippet5: jsonResponse.snippet5.startsWith("<div>") ? jsonResponse.snippet5 : this.convertTextToHtml(jsonResponse.snippet5),
              snippet6: jsonResponse.snippet6.startsWith("<div>") ? jsonResponse.snippet6 : this.convertTextToHtml(jsonResponse.snippet6),
              snippet7: jsonResponse.snippet7.startsWith("<div>") ? jsonResponse.snippet7 : this.convertTextToHtml(jsonResponse.snippet7)
            };
          }
        } catch {
          logger.warn("ClaudeService", "Response is not valid JSON either");
        }
      }
      throw new ClaudeApiError(
        `Expected 7 content blocks with ---BLOCK--- delimiters, but received ${blocks.length}. Check logs for full response.`,
        "content",
        true
      );
    }
    const email = String(leadData.email || "");
    const firstName = String(leadData.first_name || leadData.firstName || "");
    const lastName = String(leadData.last_name || leadData.lastName || "");
    const company = String(leadData.company || "");
    const title = String(leadData.title || "");
    const linkedinUrl = String(leadData.linkedin_url || leadData.linkedin || "");
    const industry = String(leadData.industry || "Technology");
    const tags = String(leadData.tags || "");
    const snippet1 = blocks[0].trim();
    const snippet2 = this.convertTextToHtml(blocks[1].trim());
    const snippet3 = blocks[2].trim();
    const snippet4 = this.convertTextToHtml(blocks[3].trim());
    const snippet5 = this.convertTextToHtml(blocks[4].trim());
    const snippet6 = this.convertTextToHtml(blocks[5].trim());
    const snippet7 = this.convertTextToHtml(blocks[6].trim());
    return {
      email,
      first_name: firstName,
      last_name: lastName,
      company,
      title,
      linkedin_url: linkedinUrl,
      tags,
      industry,
      snippet1,
      snippet2,
      snippet3,
      snippet4,
      snippet5,
      snippet6,
      snippet7
    };
  }
  async generateContent(prompt, leadData, modelId = "claude-sonnet-4-20250514", systemPrompt, fileIds) {
    logger.info("ClaudeService", `Starting API call with model: ${modelId}`);
    logger.debug("ClaudeService", `Prompt length: ${prompt.length} characters`);
    logger.debug("ClaudeService", `Request count before call: ${this.requestCount}`);
    const startTime = Date.now();
    try {
      await this.checkRateLimit();
      logger.debug("ClaudeService", "Rate limit check passed");
      logger.info("ClaudeService", "Making API call to Claude...");
      const messageContent = [
        {
          type: "text",
          text: prompt
        }
      ];
      if (fileIds && fileIds.length > 0) {
        logger.debug("ClaudeService", `Adding file references: ${fileIds}`);
        fileIds.forEach((fileId) => {
          messageContent.push({
            type: "image",
            source: {
              type: "file",
              file_id: fileId
            }
          });
        });
      }
      const apiCall = {
        model: modelId,
        max_tokens: 4e3,
        temperature: 0.7,
        messages: [
          {
            role: "user",
            content: messageContent
          }
        ]
      };
      if (systemPrompt) {
        apiCall.system = systemPrompt;
        logger.debug("ClaudeService", `Using system prompt: ${systemPrompt.length} characters`);
      }
      const response = await this.client.messages.create(apiCall);
      const duration = Date.now() - startTime;
      logger.info("ClaudeService", `Received response from Claude API in ${duration}ms`);
      logger.debug("ClaudeService", `Usage info: ${JSON.stringify(response.usage)}`);
      const content = response.content[0];
      logger.debug("ClaudeService", `Response type: ${content.type}`);
      if (content.type !== "text") {
        throw new ClaudeApiError(
          "Unexpected response format from Claude API",
          "content",
          true
        );
      }
      logger.debug("ClaudeService", `Response text length: ${content.text.length} characters`);
      logger.debug("ClaudeService", `First 200 chars of response: ${content.text.substring(0, 200)}`);
      const parsedResponse = this.parseTextBlocks(content.text, leadData);
      logger.debug("ClaudeService", `Parsed into ${Object.keys(parsedResponse).length} fields`);
      const requiredSnippets = [
        "snippet1",
        "snippet2",
        "snippet3",
        "snippet4",
        "snippet5",
        "snippet6",
        "snippet7"
      ];
      for (const field of requiredSnippets) {
        if (!parsedResponse[field]) {
          logger.error("ClaudeService", `Missing required field: ${field}`);
          logger.debug("ClaudeService", `Available fields: ${Object.keys(parsedResponse)}`);
          throw new ClaudeApiError(
            `Missing required field: ${field}`,
            "content",
            true
          );
        }
      }
      logger.info("ClaudeService", "All required fields present, returning response");
      return parsedResponse;
    } catch (error) {
      logger.error("ClaudeService", "Error occurred", error instanceof Error ? error : new Error(String(error)));
      if (error instanceof ClaudeApiError) {
        throw error;
      }
      if (error instanceof Anthropic.APIError) {
        logger.error("ClaudeService", `Anthropic API Error: ${error.status} ${error.message}`);
        if (error.status === 429) {
          throw new ClaudeApiError(
            "Rate limit exceeded. Please wait before trying again.",
            "rate_limit",
            true
          );
        } else if (error.status === 401) {
          throw new ClaudeApiError(
            "Invalid API key. Please check your CLAUDE_API_KEY configuration.",
            "auth",
            false
          );
        } else if (error.status === 403) {
          throw new ClaudeApiError(
            "Access forbidden. You may not have access to this model or have exceeded your quota.",
            "auth",
            false
          );
        } else if (error.status === 400) {
          if (error.message?.includes("model") || error.message?.includes("Model")) {
            throw new ClaudeApiError(
              `Model error: ${error.message}. The selected model may not be available or supported.`,
              "content",
              false
            );
          } else {
            throw new ClaudeApiError(
              `Bad request: ${error.message}`,
              "content",
              false
            );
          }
        } else if (error.status === 404) {
          throw new ClaudeApiError(
            "Model not found. The selected model may not exist or you may not have access to it.",
            "content",
            false
          );
        } else if (error.status >= 500) {
          throw new ClaudeApiError(
            "Claude API server error. Please try again later.",
            "network",
            true
          );
        } else {
          throw new ClaudeApiError(
            `Claude API error (${error.status}): ${error.message}`,
            "unknown",
            true
          );
        }
      }
      if (error instanceof Error && error.message.includes("network")) {
        throw new ClaudeApiError(
          "Network error. Please check your connection and try again.",
          "network",
          true
        );
      }
      throw new ClaudeApiError(
        `Unexpected error: ${error instanceof Error ? error.message : "Unknown error"}`,
        "unknown",
        true
      );
    }
  }
  async generateContentWithRetry(prompt, leadData, maxRetries = 3, modelId, systemPrompt, fileIds) {
    let lastError;
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await this.generateContent(prompt, leadData, modelId, systemPrompt, fileIds);
      } catch (error) {
        if (!(error instanceof ClaudeApiError)) {
          throw error;
        }
        lastError = error;
        if (!error.retryable) {
          logger.info("ClaudeService", `Error is not retryable (${error.category}), failing immediately`);
          throw error;
        }
        if (error.category === "auth" || error.category === "content") {
          logger.info("ClaudeService", `${error.category} error detected, stopping retries`);
          throw error;
        }
        if (attempt === maxRetries) {
          throw error;
        }
        const delay = Math.pow(2, attempt) * 1e3;
        logger.info("ClaudeService", `Retrying in ${delay}ms (attempt ${attempt + 1}/${maxRetries})`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
    throw lastError;
  }
  getRequestCount() {
    this.resetRateLimitIfNeeded();
    return this.requestCount;
  }
  getRemainingRequests() {
    this.resetRateLimitIfNeeded();
    return Math.max(0, this.MAX_REQUESTS_PER_MINUTE - this.requestCount);
  }
  // Files API methods
  async uploadFile(file, filename, mimeType) {
    logger.info("ClaudeService", `Uploading file: ${filename} (${file.length} bytes)`);
    try {
      const formData = new FormData();
      const blob = new Blob([file], { type: mimeType });
      formData.append("file", blob, filename);
      const response = await fetch("https://api.anthropic.com/v1/files", {
        method: "POST",
        headers: {
          "x-api-key": this.client.apiKey,
          "anthropic-version": "2023-06-01",
          "anthropic-beta": "files-api-2025-04-14"
        },
        body: formData
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new ClaudeApiError(
          `File upload failed: ${errorData.error?.message || response.statusText}`,
          "network",
          true
        );
      }
      const result = await response.json();
      logger.info("ClaudeService", `File uploaded successfully: ${result.id}`);
      return result.id;
    } catch (error) {
      logger.error("ClaudeService", "File upload error", error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }
  async deleteFile(fileId) {
    logger.info("ClaudeService", `Deleting file: ${fileId}`);
    try {
      const response = await fetch(`https://api.anthropic.com/v1/files/${fileId}`, {
        method: "DELETE",
        headers: {
          "x-api-key": this.client.apiKey,
          "anthropic-version": "2023-06-01",
          "anthropic-beta": "files-api-2025-04-14"
        }
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new ClaudeApiError(
          `File deletion failed: ${errorData.error?.message || response.statusText}`,
          "network",
          true
        );
      }
      logger.info("ClaudeService", "File deleted successfully");
    } catch (error) {
      logger.error("ClaudeService", "File deletion error", error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }
}
const createClaudeService = (apiKey) => new ClaudeService(apiKey);
let claudeService = null;
function initializeClaudeService() {
  if (!claudeService) {
    try {
      claudeService = createClaudeService();
      logger.info("ClaudeHandlers", "Claude service initialized successfully");
    } catch (error) {
      logger.error("ClaudeHandlers", "Failed to initialize Claude service", error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }
  return claudeService;
}
function setupClaudeHandlers() {
  logger.info("ClaudeHandlers", "Setting up Claude IPC handlers...");
  ipcMain.handle("ipc:claude:generateContent", async (event, request) => {
    logIpcOperation("claude:generateContent", {
      promptLength: request.prompt?.length,
      modelId: request.modelId,
      hasSystemPrompt: !!request.systemPrompt,
      fileCount: request.fileIds?.length || 0,
      maxRetries: request.maxRetries
    });
    try {
      validateInput(request, ["prompt", "leadData"]);
      const sanitizedRequest = sanitizeInput(request);
      if (!sanitizedRequest.prompt || sanitizedRequest.prompt.trim().length === 0) {
        throw new Error("Prompt cannot be empty");
      }
      if (sanitizedRequest.prompt.length > 1e5) {
        throw new Error("Prompt is too long (max 100,000 characters)");
      }
      if (!sanitizedRequest.leadData || typeof sanitizedRequest.leadData !== "object") {
        throw new Error("Lead data must be a valid object");
      }
      const service = initializeClaudeService();
      const result = await service.generateContentWithRetry(
        sanitizedRequest.prompt,
        sanitizedRequest.leadData,
        sanitizedRequest.maxRetries || 3,
        sanitizedRequest.modelId,
        sanitizedRequest.systemPrompt,
        sanitizedRequest.fileIds
      );
      logger.info("ClaudeHandlers", "Content generation completed successfully");
      return createSuccessResponse(result);
    } catch (error) {
      logger.error("ClaudeHandlers", "Content generation failed", error instanceof Error ? error : new Error(String(error)));
      return handleIpcError(error, "claude:generateContent");
    }
  });
  ipcMain.handle("ipc:claude:uploadFile", async (event, request) => {
    logIpcOperation("claude:uploadFile", {
      filename: request.filename,
      mimeType: request.mimeType,
      size: request.fileBuffer?.byteLength
    });
    try {
      validateInput(request, ["fileBuffer", "filename", "mimeType"]);
      const maxSize = 10 * 1024 * 1024;
      if (request.fileBuffer.byteLength > maxSize) {
        throw new Error(`File size exceeds maximum allowed size of ${maxSize / 1024 / 1024}MB`);
      }
      const allowedTypes = [
        "image/jpeg",
        "image/jpg",
        "image/png",
        "image/gif",
        "image/webp",
        "application/pdf",
        "text/plain",
        "text/csv",
        "application/json"
      ];
      if (!allowedTypes.includes(request.mimeType)) {
        throw new Error(`File type ${request.mimeType} is not supported`);
      }
      const service = initializeClaudeService();
      const buffer = Buffer.from(request.fileBuffer);
      const fileId = await service.uploadFile(buffer, request.filename, request.mimeType);
      logger.info("ClaudeHandlers", `File uploaded successfully: ${fileId}`);
      return createSuccessResponse({ fileId });
    } catch (error) {
      logger.error("ClaudeHandlers", "File upload failed", error instanceof Error ? error : new Error(String(error)));
      return handleIpcError(error, "claude:uploadFile");
    }
  });
  ipcMain.handle("ipc:claude:deleteFile", async (event, fileId) => {
    logIpcOperation("claude:deleteFile", { fileId });
    try {
      if (!fileId || typeof fileId !== "string" || fileId.trim().length === 0) {
        throw new Error("File ID is required and must be a non-empty string");
      }
      const service = initializeClaudeService();
      await service.deleteFile(fileId.trim());
      logger.info("ClaudeHandlers", `File deleted successfully: ${fileId}`);
      return createSuccessResponse({ success: true });
    } catch (error) {
      logger.error("ClaudeHandlers", "File deletion failed", error instanceof Error ? error : new Error(String(error)));
      return handleIpcError(error, "claude:deleteFile");
    }
  });
  ipcMain.handle("ipc:claude:getQuotaInfo", async (event) => {
    logIpcOperation("claude:getQuotaInfo");
    try {
      const service = initializeClaudeService();
      const quotaInfo = {
        requestCount: service.getRequestCount(),
        remainingRequests: service.getRemainingRequests(),
        maxRequestsPerMinute: 100
      };
      logger.debug("ClaudeHandlers", `Quota info retrieved: ${JSON.stringify(quotaInfo)}`);
      return createSuccessResponse(quotaInfo);
    } catch (error) {
      logger.error("ClaudeHandlers", "Failed to get quota info", error instanceof Error ? error : new Error(String(error)));
      return handleIpcError(error, "claude:getQuotaInfo");
    }
  });
  logger.info("ClaudeHandlers", "Claude IPC handlers setup complete");
}
class WoodpeckerApiError extends Error {
  category;
  retryable;
  constructor(message, category, retryable = false) {
    super(message);
    this.name = "WoodpeckerApiError";
    this.category = category;
    this.retryable = retryable;
  }
}
class WoodpeckerService {
  apiKey;
  baseUrl = "https://api.woodpecker.co/rest/v1";
  rateLimitDelay = 650;
  // 100 req/min = ~600ms between requests, adding buffer
  cachedCampaigns = null;
  cacheExpiry = null;
  cacheTimeout = 5 * 60 * 1e3;
  // 5 minutes
  MAX_REQUESTS_PER_MINUTE = 100;
  requestCount = 0;
  lastResetTime = Date.now();
  constructor(apiKey) {
    const key = apiKey || process.env.WOODPECKER_API_KEY;
    logger.info("WoodpeckerService", "Initializing Woodpecker service...");
    logger.debug("WoodpeckerService", `API key provided via param: ${!!apiKey}`);
    logger.debug("WoodpeckerService", `API key from env: ${!!process.env.WOODPECKER_API_KEY}`);
    logger.debug("WoodpeckerService", `Final key available: ${!!key && key !== "replace"}`);
    if (!key || key === "replace") {
      logger.warn("WoodpeckerService", "No valid API key found - service will run in demo mode");
      this.apiKey = "";
    } else {
      this.apiKey = key;
    }
    logger.info("WoodpeckerService", "Woodpecker service initialized successfully");
  }
  resetRateLimitIfNeeded() {
    const now = Date.now();
    if (now - this.lastResetTime >= 6e4) {
      this.requestCount = 0;
      this.lastResetTime = now;
    }
  }
  async checkRateLimit() {
    this.resetRateLimitIfNeeded();
    if (this.requestCount >= this.MAX_REQUESTS_PER_MINUTE) {
      const waitTime = 6e4 - (Date.now() - this.lastResetTime);
      throw new WoodpeckerApiError(
        `Rate limit reached. Please wait ${Math.ceil(waitTime / 1e3)} seconds.`,
        "rate_limit",
        true
      );
    }
    this.requestCount++;
  }
  getHeaders() {
    return {
      "x-api-key": this.apiKey,
      "Content-Type": "application/json"
    };
  }
  async delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
  async makeRequest(endpoint, options = {}) {
    await this.checkRateLimit();
    const url = `${this.baseUrl}${endpoint}`;
    logger.debug("WoodpeckerService", `Making API request to: ${url}`, {
      method: options.method || "GET",
      endpoint
    });
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.getHeaders(),
          ...options.headers
        }
      });
      logger.debug("WoodpeckerService", `Response received: ${response.status} ${response.statusText}`);
      const data = await response.json();
      if (!response.ok) {
        const errorMsg = data?.status?.msg || `HTTP ${response.status}`;
        logger.error("WoodpeckerService", `API error: ${errorMsg}`, {
          status: response.status,
          data
        });
        let category = "unknown";
        if (response.status === 401 || response.status === 403) {
          category = "auth";
        } else if (response.status === 429) {
          category = "rate_limit";
        } else if (response.status >= 400 && response.status < 500) {
          category = "validation";
        } else if (response.status >= 500) {
          category = "network";
        }
        throw new WoodpeckerApiError(`Woodpecker API error: ${errorMsg}`, category, category === "rate_limit" || category === "network");
      }
      return data;
    } catch (error) {
      if (error instanceof WoodpeckerApiError) {
        throw error;
      }
      logger.error("WoodpeckerService", "Request failed", error instanceof Error ? error : new Error(String(error)));
      if (error instanceof Error) {
        throw new WoodpeckerApiError(error.message, "network", true);
      }
      throw new WoodpeckerApiError("Unknown error occurred while calling Woodpecker API", "unknown", false);
    }
  }
  async getCampaigns(forceRefresh = false) {
    logger.info("WoodpeckerService", "Getting campaigns...", {
      forceRefresh,
      hasCachedCampaigns: !!this.cachedCampaigns,
      cacheValid: this.cacheExpiry ? Date.now() < this.cacheExpiry : false
    });
    if (!forceRefresh && this.cachedCampaigns && this.cacheExpiry && Date.now() < this.cacheExpiry) {
      logger.debug("WoodpeckerService", "Returning cached campaigns");
      return this.cachedCampaigns;
    }
    if (!this.apiKey || this.apiKey.trim() === "" || this.apiKey === "replace") {
      logger.warn("WoodpeckerService", "No API key, using mock campaigns");
      return this.getMockCampaigns();
    }
    try {
      logger.debug("WoodpeckerService", "Fetching campaigns from API...");
      const response = await this.makeRequest("/campaign_list");
      this.cachedCampaigns = Array.isArray(response) ? response.map((campaign) => ({
        campaign_id: campaign.id,
        name: campaign.name,
        status: campaign.status,
        created_date: campaign.created,
        prospects_count: void 0
        // Not provided by this endpoint
      })) : [];
      this.cacheExpiry = Date.now() + this.cacheTimeout;
      logger.info("WoodpeckerService", `Campaigns fetched and cached: ${this.cachedCampaigns.length} campaigns`);
      return this.cachedCampaigns;
    } catch (error) {
      logger.error("WoodpeckerService", "Failed to fetch campaigns", error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }
  getMockCampaigns() {
    const mockCampaigns = [
      {
        campaign_id: 2356837,
        name: "Q4 Outreach Campaign",
        status: "ACTIVE",
        created_date: "2024-12-01T10:00:00Z",
        prospects_count: 145
      },
      {
        campaign_id: 1234567,
        name: "SaaS CEOs",
        status: "RUNNING",
        created_date: "2024-11-15T14:30:00Z",
        prospects_count: 89
      },
      {
        campaign_id: 7654321,
        name: "Holiday Follow-up",
        status: "PAUSED",
        created_date: "2024-12-10T09:15:00Z",
        prospects_count: 234
      },
      {
        campaign_id: 9876543,
        name: "New Year Prospects",
        status: "DRAFT",
        created_date: "2024-12-20T16:45:00Z",
        prospects_count: 67
      }
    ];
    this.cachedCampaigns = mockCampaigns;
    this.cacheExpiry = Date.now() + this.cacheTimeout;
    return mockCampaigns;
  }
  async detectProspectTimezones(prospectIds) {
    logger.debug("WoodpeckerService", `Detecting timezones for ${prospectIds.length} prospects`);
    try {
      const bulkOperationId = crypto.randomUUID();
      const response = await this.makeRequest(
        `/prospects/bulk/${bulkOperationId}`,
        {
          method: "POST",
          body: JSON.stringify({
            type: "DETECT_TIMEZONE",
            prospect_ids: prospectIds
          })
        }
      );
      logger.info("WoodpeckerService", "Timezone detection initiated successfully");
      return response;
    } catch (error) {
      logger.warn("WoodpeckerService", "Failed to detect timezones (non-critical)", error instanceof Error ? error : new Error(String(error)));
    }
  }
  async addProspectsToCampaign(prospects, campaignId, onProgress) {
    logger.info("WoodpeckerService", `Starting export of ${prospects.length} prospects to campaign ${campaignId}`);
    const progress = {
      current: 0,
      total: prospects.length,
      succeeded: 0,
      failed: 0,
      status: "processing",
      errors: []
    };
    if (prospects.length === 0) {
      logger.warn("WoodpeckerService", "No prospects to export");
      progress.status = "completed";
      return progress;
    }
    if (!this.apiKey || this.apiKey.trim() === "" || this.apiKey === "replace") {
      logger.warn("WoodpeckerService", "Demo mode - simulating export");
      return this.simulateExport(prospects, campaignId, onProgress);
    }
    const BATCH_SIZE = 50;
    const batches = [];
    for (let i = 0; i < prospects.length; i += BATCH_SIZE) {
      batches.push(prospects.slice(i, i + BATCH_SIZE));
    }
    try {
      for (const batch of batches) {
        const request = {
          prospects: batch,
          campaign: {
            campaign_id: campaignId
          },
          force: false
        };
        try {
          logger.debug("WoodpeckerService", `Sending batch ${batches.indexOf(batch) + 1}/${batches.length} (${batch.length} prospects)`);
          const response = await this.makeRequest(
            "/add_prospects_campaign",
            {
              method: "POST",
              body: JSON.stringify(request)
            }
          );
          logger.debug("WoodpeckerService", "Batch response received", {
            hasProspects: !!response.prospects,
            prospectsLength: response.prospects?.length,
            statusInfo: response.status
          });
          const addedProspectIds = [];
          if (response.prospects) {
            response.prospects.forEach((prospect) => {
              progress.current++;
              const isSuccess = prospect.status === "OK" || prospect.status === "DUPLICATE" || prospect.status === "SUCCESS" || prospect.result === "OK" || prospect.result === "SUCCESS" || !prospect.error && !prospect.status;
              if (isSuccess) {
                logger.debug("WoodpeckerService", `Prospect ${prospect.email} succeeded`);
                progress.succeeded++;
                if (prospect.id) {
                  addedProspectIds.push(prospect.id);
                }
              } else {
                const errorMessage = prospect.msg || prospect.message || prospect.error || prospect.status || (prospect.status === void 0 ? "API response missing status field" : "Unknown error");
                logger.debug("WoodpeckerService", `Prospect ${prospect.email} failed: ${errorMessage}`);
                progress.failed++;
                progress.errors.push({
                  email: prospect.email,
                  error: errorMessage
                });
              }
            });
          } else {
            batch.forEach((_prospect) => {
              progress.current++;
              progress.succeeded++;
            });
          }
          if (addedProspectIds.length > 0) {
            logger.debug("WoodpeckerService", `Triggering timezone detection for ${addedProspectIds.length} prospects`);
            this.detectProspectTimezones(addedProspectIds).catch(
              (err) => logger.warn("WoodpeckerService", "Timezone detection failed but prospects were added", err instanceof Error ? err : new Error(String(err)))
            );
          }
          if (onProgress) {
            onProgress({ ...progress });
          }
          if (batches.indexOf(batch) < batches.length - 1) {
            await this.delay(this.rateLimitDelay);
          }
        } catch (error) {
          batch.forEach((_prospect) => {
            progress.current++;
            progress.failed++;
            progress.errors.push({
              email: _prospect.email,
              error: error instanceof Error ? error.message : "Unknown error"
            });
          });
          if (onProgress) {
            onProgress({ ...progress });
          }
        }
      }
      progress.status = progress.failed === 0 ? "completed" : "completed";
      return progress;
    } catch (error) {
      progress.status = "error";
      throw error;
    }
  }
  async simulateExport(prospects, campaignId, onProgress) {
    logger.warn("WoodpeckerService", `Demo mode: Simulating export of ${prospects.length} prospects to campaign ${campaignId}`);
    const progress = {
      current: 0,
      total: prospects.length,
      succeeded: 0,
      failed: 0,
      status: "processing",
      errors: []
    };
    for (const prospect of prospects) {
      await this.delay(200);
      progress.current++;
      progress.succeeded++;
      if (onProgress) {
        onProgress({ ...progress });
      }
    }
    progress.status = "completed";
    return progress;
  }
  async checkDuplicateProspects(emails, campaignId) {
    logger.debug("WoodpeckerService", `Checking for duplicates: ${emails.length} emails in campaign ${campaignId}`);
    try {
      const response = await this.makeRequest(`/prospects?campaign_id=${campaignId}`);
      logger.debug("WoodpeckerService", "Duplicate check response received");
      const existingEmails = new Set(
        response.prospects.filter((p) => p.campaigns.includes(campaignId)).map((p) => p.email.toLowerCase())
      );
      const duplicates = emails.filter((email) => existingEmails.has(email.toLowerCase()));
      logger.info("WoodpeckerService", `Duplicate check complete: ${duplicates.length} duplicates found out of ${emails.length} emails`);
      return duplicates;
    } catch (error) {
      logger.error("WoodpeckerService", "Failed to check duplicates", error instanceof Error ? error : new Error(String(error)));
      return [];
    }
  }
  clearCampaignCache() {
    this.cachedCampaigns = null;
    this.cacheExpiry = null;
    logger.debug("WoodpeckerService", "Campaign cache cleared");
  }
  // Quota management methods
  getQuotaInfo() {
    this.resetRateLimitIfNeeded();
    return {
      requestCount: this.requestCount,
      remainingRequests: this.MAX_REQUESTS_PER_MINUTE - this.requestCount,
      maxRequestsPerMinute: this.MAX_REQUESTS_PER_MINUTE
    };
  }
}
function createWoodpeckerService(apiKey) {
  return new WoodpeckerService(apiKey);
}
let woodpeckerService = null;
function initializeWoodpeckerService() {
  if (!woodpeckerService) {
    try {
      woodpeckerService = createWoodpeckerService();
      logger.info("WoodpeckerHandlers", "Woodpecker service initialized successfully");
    } catch (error) {
      logger.error("WoodpeckerHandlers", "Failed to initialize Woodpecker service", error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }
  return woodpeckerService;
}
function setupWoodpeckerHandlers() {
  logger.info("WoodpeckerHandlers", "Setting up Woodpecker IPC handlers...");
  ipcMain.handle("ipc:woodpecker:getCampaigns", async (event, request = {}) => {
    logIpcOperation("woodpecker:getCampaigns", {
      forceRefresh: request.forceRefresh
    });
    try {
      const sanitizedRequest = sanitizeInput(request);
      const service = initializeWoodpeckerService();
      const campaigns = await service.getCampaigns(sanitizedRequest.forceRefresh);
      logger.info("WoodpeckerHandlers", `Retrieved ${campaigns.length} campaigns successfully`);
      return createSuccessResponse(campaigns);
    } catch (error) {
      logger.error("WoodpeckerHandlers", "Failed to get campaigns", error instanceof Error ? error : new Error(String(error)));
      return handleIpcError(error, "woodpecker:getCampaigns");
    }
  });
  ipcMain.handle("ipc:woodpecker:addProspects", async (event, request) => {
    logIpcOperation("woodpecker:addProspects", {
      prospectsCount: request.prospects?.length,
      campaignId: request.campaignId,
      force: request.force
    });
    try {
      validateInput(request, ["prospects", "campaignId"]);
      const sanitizedRequest = sanitizeInput(request);
      if (!Array.isArray(sanitizedRequest.prospects) || sanitizedRequest.prospects.length === 0) {
        throw new Error("Prospects must be a non-empty array");
      }
      if (!Number.isInteger(sanitizedRequest.campaignId) || sanitizedRequest.campaignId <= 0) {
        throw new Error("Campaign ID must be a positive integer");
      }
      for (const prospect of sanitizedRequest.prospects) {
        if (!prospect.email || typeof prospect.email !== "string") {
          throw new Error("Each prospect must have a valid email address");
        }
        if (!prospect.first_name || typeof prospect.first_name !== "string") {
          throw new Error("Each prospect must have a valid first name");
        }
      }
      const service = initializeWoodpeckerService();
      const progress = await service.addProspectsToCampaign(
        sanitizedRequest.prospects,
        sanitizedRequest.campaignId,
        sanitizedRequest.force,
        sanitizedRequest.onProgress
      );
      logger.info("WoodpeckerHandlers", `Added prospects to campaign successfully: ${progress.succeeded} succeeded, ${progress.failed} failed`);
      return createSuccessResponse(progress);
    } catch (error) {
      logger.error("WoodpeckerHandlers", "Failed to add prospects", error instanceof Error ? error : new Error(String(error)));
      return handleIpcError(error, "woodpecker:addProspects");
    }
  });
  ipcMain.handle("ipc:woodpecker:checkDuplicates", async (event, request) => {
    logIpcOperation("woodpecker:checkDuplicates", {
      emailCount: request.emails?.length,
      campaignId: request.campaignId
    });
    try {
      validateInput(request, ["emails", "campaignId"]);
      const sanitizedRequest = sanitizeInput(request);
      if (!Array.isArray(sanitizedRequest.emails) || sanitizedRequest.emails.length === 0) {
        throw new Error("Emails must be a non-empty array");
      }
      if (!Number.isInteger(sanitizedRequest.campaignId) || sanitizedRequest.campaignId <= 0) {
        throw new Error("Campaign ID must be a positive integer");
      }
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      for (const email of sanitizedRequest.emails) {
        if (!email || typeof email !== "string" || !emailRegex.test(email)) {
          throw new Error(`Invalid email format: ${email}`);
        }
      }
      const service = initializeWoodpeckerService();
      const duplicates = await service.checkDuplicateProspects(
        sanitizedRequest.emails,
        sanitizedRequest.campaignId
      );
      logger.info("WoodpeckerHandlers", `Checked duplicates: ${duplicates.length} found out of ${sanitizedRequest.emails.length} emails`);
      return createSuccessResponse(duplicates);
    } catch (error) {
      logger.error("WoodpeckerHandlers", "Failed to check duplicates", error instanceof Error ? error : new Error(String(error)));
      return handleIpcError(error, "woodpecker:checkDuplicates");
    }
  });
  ipcMain.handle("ipc:woodpecker:clearCache", async (event) => {
    logIpcOperation("woodpecker:clearCache");
    try {
      const service = initializeWoodpeckerService();
      service.clearCampaignCache();
      logger.info("WoodpeckerHandlers", "Campaign cache cleared successfully");
      return createSuccessResponse({ success: true });
    } catch (error) {
      logger.error("WoodpeckerHandlers", "Failed to clear cache", error instanceof Error ? error : new Error(String(error)));
      return handleIpcError(error, "woodpecker:clearCache");
    }
  });
  ipcMain.handle("ipc:woodpecker:getQuotaInfo", async (event) => {
    logIpcOperation("woodpecker:getQuotaInfo");
    try {
      const service = initializeWoodpeckerService();
      const quotaInfo = {
        requestCount: service.getRequestCount(),
        remainingRequests: service.getRemainingRequests(),
        maxRequestsPerMinute: 100
      };
      logger.debug("WoodpeckerHandlers", `Quota info retrieved: ${JSON.stringify(quotaInfo)}`);
      return createSuccessResponse(quotaInfo);
    } catch (error) {
      logger.error("WoodpeckerHandlers", "Failed to get quota info", error instanceof Error ? error : new Error(String(error)));
      return handleIpcError(error, "woodpecker:getQuotaInfo");
    }
  });
  logger.info("WoodpeckerHandlers", "Woodpecker IPC handlers setup complete");
}
function setupIpcHandlers(appDataPath2) {
  console.log("Setting up IPC handlers...");
  try {
    setupImportsHandlers(appDataPath2);
    setupLeadsHandlers(appDataPath2);
    setupGeneratedContentHandlers(appDataPath2);
    setupMappingsHandlers(appDataPath2);
    setupAppMetadataHandlers(appDataPath2);
    setupAdvancedQueriesHandlers(appDataPath2);
    setupClaudeHandlers();
    setupWoodpeckerHandlers();
    console.log("All IPC handlers setup successfully");
  } catch (error) {
    console.error("Failed to setup IPC handlers:", error);
    throw error;
  }
}
const __dirname = path$2.dirname(fileURLToPath(import.meta.url));
process.env.APP_ROOT = path$2.join(__dirname, "../..");
const MAIN_DIST = path$2.join(process.env.APP_ROOT, "dist-electron");
const RENDERER_DIST = path$2.join(process.env.APP_ROOT, "dist");
const VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL;
process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL ? path$2.join(process.env.APP_ROOT, "public") : RENDERER_DIST;
if (process.platform === "win32") {
  app.disableHardwareAcceleration();
}
if (process.platform === "win32") {
  app.setAppUserModelId(app.getName());
}
if (!app.requestSingleInstanceLock()) {
  app.quit();
  process.exit(0);
}
let win = null;
const preload = path$2.join(__dirname, "../preload/preload.mjs");
const indexHtml = path$2.join(RENDERER_DIST, "index.html");
function createApplicationMenu() {
  const template = [
    {
      label: "File",
      submenu: [
        {
          label: "New Import",
          accelerator: "CmdOrCtrl+N",
          click: () => {
            win?.webContents.send("menu-action", "new-import");
          }
        },
        { type: "separator" },
        {
          label: "Exit",
          accelerator: process.platform === "darwin" ? "Cmd+Q" : "Ctrl+Q",
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: "Edit",
      submenu: [
        { role: "undo" },
        { role: "redo" },
        { type: "separator" },
        { role: "cut" },
        { role: "copy" },
        { role: "paste" },
        { role: "selectall" }
      ]
    },
    {
      label: "View",
      submenu: [
        { role: "reload" },
        { role: "forceReload" },
        { role: "toggleDevTools" },
        { type: "separator" },
        { role: "resetZoom" },
        { role: "zoomIn" },
        { role: "zoomOut" },
        { type: "separator" },
        { role: "togglefullscreen" }
      ]
    },
    {
      label: "Window",
      submenu: [
        { role: "minimize" },
        { role: "close" }
      ]
    },
    {
      label: "Help",
      submenu: [
        {
          label: "About Woodpecker API",
          click: () => {
            win?.webContents.send("menu-action", "about");
          }
        },
        {
          label: "Learn More",
          click: () => {
            shell.openExternal("https://github.com/your-repo/woodpecker-api");
          }
        }
      ]
    }
  ];
  if (process.platform === "darwin") {
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: "about" },
        { type: "separator" },
        { role: "services" },
        { type: "separator" },
        { role: "hide" },
        { role: "hideOthers" },
        { role: "unhide" },
        { type: "separator" },
        { role: "quit" }
      ]
    });
    template[4].submenu = [
      { role: "close" },
      { role: "minimize" },
      { role: "zoom" },
      { type: "separator" },
      { role: "front" }
    ];
  }
  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}
async function createWindow() {
  win = new BrowserWindow({
    title: "Woodpecker API",
    icon: path$2.join(process.env.VITE_PUBLIC, "favicon.ico"),
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    center: true,
    show: false,
    // Don't show until ready
    titleBarStyle: process.platform === "darwin" ? "hiddenInset" : "default",
    webPreferences: {
      preload,
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false,
      sandbox: false,
      // Required for IPC bridge
      spellcheck: false
    }
  });
  win.once("ready-to-show", () => {
    if (win) {
      win.show();
      if (process.platform === "darwin") {
        win.focus();
      }
    }
  });
  win.on("closed", () => {
    win = null;
  });
  win.on("close", (event) => {
    if (process.platform === "darwin") {
      event.preventDefault();
      win?.hide();
    }
  });
  try {
    if (VITE_DEV_SERVER_URL) {
      await win.loadURL(VITE_DEV_SERVER_URL);
      win.webContents.openDevTools();
    } else {
      await win.loadFile(indexHtml);
    }
  } catch (error) {
    logger.error("Window", "Failed to load application content", error instanceof Error ? error : new Error(String(error)));
    throw error;
  }
  win.webContents.on("did-finish-load", () => {
    logger.info("Window", "Application content loaded successfully");
    win?.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  win.webContents.on("did-fail-load", (event, errorCode, errorDescription) => {
    logger.error("Window", `Failed to load content: ${errorDescription} (${errorCode})`);
  });
  win.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith("https:") || url.startsWith("http:")) {
      shell.openExternal(url);
    }
    return { action: "deny" };
  });
}
app.whenReady().then(async () => {
  try {
    logger.info("App", "Application starting up");
    const userDataPath = app.getPath("userData");
    setAppDataPath(userDataPath);
    let dbInitialized = false;
    let retryCount = 0;
    const maxRetries = 3;
    while (!dbInitialized && retryCount < maxRetries) {
      try {
        await initializeDatabase();
        dbInitialized = true;
        logger.info("Database", "Database initialized successfully");
      } catch (dbError) {
        retryCount++;
        logger.error("Database", `Database initialization attempt ${retryCount} failed`, dbError instanceof Error ? dbError : new Error(String(dbError)));
        if (retryCount >= maxRetries) {
          throw new Error(`Failed to initialize database after ${maxRetries} attempts: ${dbError instanceof Error ? dbError.message : String(dbError)}`);
        }
        await new Promise((resolve) => setTimeout(resolve, 1e3 * retryCount));
      }
    }
    setupIpcHandlers();
    logger.info("IPC", "IPC handlers setup complete");
    createApplicationMenu();
    logger.info("App", "Application menu created");
    await createWindow();
    logger.info("App", "Main window created successfully");
    logger.info("App", "Application startup completed successfully");
  } catch (error) {
    logger.error("App", "Failed to initialize application", error instanceof Error ? error : new Error(String(error)));
    dialog.showErrorBox(
      "Application Startup Error",
      `Failed to start Woodpecker API: ${error instanceof Error ? error.message : String(error)}

The application will now exit.`
    );
    app.quit();
  }
});
app.on("window-all-closed", () => {
  logger.info("App", "All windows closed");
  win = null;
  if (process.platform !== "darwin") {
    logger.info("App", "Quitting application");
    app.quit();
  }
});
app.on("second-instance", () => {
  logger.info("App", "Second instance detected, focusing main window");
  if (win) {
    if (win.isMinimized()) {
      win.restore();
    }
    if (!win.isVisible()) {
      win.show();
    }
    win.focus();
  }
});
app.on("activate", () => {
  logger.info("App", "Application activated");
  const allWindows = BrowserWindow.getAllWindows();
  if (allWindows.length) {
    allWindows[0].focus();
  } else {
    createWindow().catch((error) => {
      logger.error("App", "Failed to recreate window on activate", error instanceof Error ? error : new Error(String(error)));
    });
  }
});
app.on("before-quit", (event) => {
  logger.info("App", "Application preparing to quit");
});
app.on("will-quit", (event) => {
  logger.info("App", "Application will quit");
});
app.on("web-contents-created", (_, contents) => {
  logger.debug("Security", "New web contents created, applying security policies");
  contents.on("will-navigate", (navigationEvent, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    const isAllowed = parsedUrl.origin === VITE_DEV_SERVER_URL || navigationUrl.startsWith("file://") || navigationUrl.startsWith("data:");
    if (!isAllowed) {
      logger.warn("Security", `Blocked navigation to: ${navigationUrl}`);
      navigationEvent.preventDefault();
    }
  });
  contents.setWindowOpenHandler(({ url }) => {
    logger.debug("Security", `Window open request for: ${url}`);
    if (url.startsWith("https:") || url.startsWith("http:")) {
      shell.openExternal(url);
      logger.info("Security", `Opened external URL in browser: ${url}`);
    } else {
      logger.warn("Security", `Blocked window open request for: ${url}`);
    }
    return { action: "deny" };
  });
  contents.session.setPermissionRequestHandler((webContents, permission, callback) => {
    logger.warn("Security", `Permission request denied: ${permission}`);
    callback(false);
  });
  contents.on("certificate-error", (event, url, error, certificate, callback) => {
    logger.error("Security", `Certificate error for ${url}: ${error}`);
    event.preventDefault();
    callback(false);
  });
});
let isShuttingDown = false;
async function gracefulShutdown(signal) {
  if (isShuttingDown) {
    logger.warn("App", `Received ${signal} during shutdown, forcing exit`);
    process.exit(1);
  }
  isShuttingDown = true;
  logger.info("App", `Received ${signal}, initiating graceful shutdown`);
  try {
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach((window2) => {
      if (!window2.isDestroyed()) {
        window2.close();
      }
    });
    await new Promise((resolve) => setTimeout(resolve, 1e3));
    logger.info("App", "Graceful shutdown completed");
    app.quit();
  } catch (error) {
    logger.error("App", "Error during graceful shutdown", error instanceof Error ? error : new Error(String(error)));
    process.exit(1);
  }
}
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));
process.on("uncaughtException", (error) => {
  logger.error("App", "Uncaught exception", error);
  gracefulShutdown("uncaughtException");
});
process.on("unhandledRejection", (reason, promise) => {
  logger.error("App", "Unhandled promise rejection", reason instanceof Error ? reason : new Error(String(reason)));
  gracefulShutdown("unhandledRejection");
});
export {
  MAIN_DIST,
  RENDERER_DIST,
  VITE_DEV_SERVER_URL
};
